/**
 * useFileOperations Hook
 * React hook for unified file operations using FileRoutingService
 */

import { useState, useCallback, useRef } from 'react'
import { fileRoutingService } from '../services/fileRoutingService'
import {
  FileDestination,
  FileUploadResponse,
  UploadedFile,
  FileDeleteResponse,
  FileListResponse,
  FileReadResponse
} from '../types/fileRouting'

export interface FileOperationState {
  uploading: boolean
  deleting: boolean
  reading: boolean
  listing: boolean
  uploadProgress: Record<string, number>
  error: string | null
}

export interface UseFileOperationsReturn {
  // State
  state: FileOperationState
  
  // Operations
  uploadFiles: (files: File[], destination: FileDestination, onProgress?: (progress: number) => void) => Promise<UploadedFile[]>
  uploadToContext: (files: File[], contextId: string) => Promise<UploadedFile[]>
  uploadToPath: (files: File[], path: string) => Promise<UploadedFile[]>
  deleteFiles: (filePaths: string[]) => Promise<string[]>
  readFile: (filePath: string) => Promise<string | Buffer | null>
  listFiles: (path: string, recursive?: boolean) => Promise<FileListResponse>
  
  // Utilities
  clearError: () => void
  resetState: () => void
}

export const useFileOperations = (): UseFileOperationsReturn => {
  const [state, setState] = useState<FileOperationState>({
    uploading: false,
    deleting: false,
    reading: false,
    listing: false,
    uploadProgress: {},
    error: null
  })

  const abortControllerRef = useRef<AbortController | null>(null)

  const updateState = useCallback((updates: Partial<FileOperationState>) => {
    setState(prev => ({ ...prev, ...updates }))
  }, [])

  const setError = useCallback((error: string | null) => {
    updateState({ error })
  }, [updateState])

  const clearError = useCallback(() => {
    setError(null)
  }, [setError])

  const resetState = useCallback(() => {
    setState({
      uploading: false,
      deleting: false,
      reading: false,
      listing: false,
      uploadProgress: {},
      error: null
    })
  }, [])

  /**
   * Upload files with progress tracking
   */
  const uploadFiles = useCallback(async (
    files: File[],
    destination: FileDestination,
    onProgress?: (progress: number) => void
  ): Promise<UploadedFile[]> => {
    updateState({ uploading: true, error: null })
    
    try {
      // Initialize progress tracking
      const progressMap: Record<string, number> = {}
      files.forEach(file => {
        progressMap[file.name] = 0
      })
      updateState({ uploadProgress: progressMap })

      // Simulate progress updates (in real implementation, this would come from the service)
      const progressInterval = setInterval(() => {
        const updatedProgress = { ...progressMap }
        let allComplete = true
        
        Object.keys(updatedProgress).forEach(filename => {
          if (updatedProgress[filename] < 100) {
            updatedProgress[filename] = Math.min(100, updatedProgress[filename] + 10)
            allComplete = false
          }
        })
        
        updateState({ uploadProgress: updatedProgress })
        
        if (onProgress) {
          const totalProgress = Object.values(updatedProgress).reduce((sum, p) => sum + p, 0) / files.length
          onProgress(totalProgress)
        }
        
        if (allComplete) {
          clearInterval(progressInterval)
        }
      }, 200)

      const result = await fileRoutingService.handleFileDrop(files, destination)
      
      clearInterval(progressInterval)
      
      if (result.success) {
        // Set all files to 100% complete
        const completedProgress: Record<string, number> = {}
        files.forEach(file => {
          completedProgress[file.name] = 100
        })
        updateState({ uploadProgress: completedProgress })
        
        if (onProgress) {
          onProgress(100)
        }
        
        return result.files
      } else {
        const errorMessage = result.errors?.[0]?.message || 'Upload failed'
        throw new Error(errorMessage)
      }
    } catch (error: any) {
      setError(error.message || 'Upload failed')
      throw error
    } finally {
      updateState({ uploading: false })
    }
  }, [updateState, setError])

  /**
   * Upload files to a specific context
   */
  const uploadToContext = useCallback(async (
    files: File[],
    contextId: string
  ): Promise<UploadedFile[]> => {
    return uploadFiles(files, { contextId, autoRoute: true })
  }, [uploadFiles])

  /**
   * Upload files to a specific path
   */
  const uploadToPath = useCallback(async (
    files: File[],
    path: string
  ): Promise<UploadedFile[]> => {
    return uploadFiles(files, { path, autoRoute: false })
  }, [uploadFiles])

  /**
   * Delete files
   */
  const deleteFiles = useCallback(async (filePaths: string[]): Promise<string[]> => {
    updateState({ deleting: true, error: null })
    
    try {
      const result = await fileRoutingService.deleteFiles({
        filePaths,
        options: { moveToTrash: true }
      })
      
      if (result.success) {
        return result.deletedFiles
      } else {
        const errorMessage = result.errors?.[0]?.message || 'Delete failed'
        throw new Error(errorMessage)
      }
    } catch (error: any) {
      setError(error.message || 'Delete failed')
      throw error
    } finally {
      updateState({ deleting: false })
    }
  }, [updateState, setError])

  /**
   * Read file content
   */
  const readFile = useCallback(async (filePath: string): Promise<string | Buffer | null> => {
    updateState({ reading: true, error: null })
    
    try {
      const result = await fileRoutingService.readFile({
        filePath
      })
      
      if (result.success) {
        return result.content || null
      } else {
        throw new Error(result.error || 'Read failed')
      }
    } catch (error: any) {
      setError(error.message || 'Read failed')
      throw error
    } finally {
      updateState({ reading: false })
    }
  }, [updateState, setError])

  /**
   * List files in directory
   */
  const listFiles = useCallback(async (
    path: string,
    recursive: boolean = false
  ): Promise<FileListResponse> => {
    updateState({ listing: true, error: null })
    
    try {
      const result = await fileRoutingService.listFiles({
        path,
        options: { recursive }
      })
      
      return result
    } catch (error: any) {
      setError(error.message || 'List failed')
      throw error
    } finally {
      updateState({ listing: false })
    }
  }, [updateState, setError])

  return {
    state,
    uploadFiles,
    uploadToContext,
    uploadToPath,
    deleteFiles,
    readFile,
    listFiles,
    clearError,
    resetState
  }
}

/**
 * Hook for context-specific file operations
 */
export const useContextFileOperations = (contextId: string) => {
  const fileOps = useFileOperations()
  
  const uploadToThisContext = useCallback(async (files: File[]) => {
    return fileOps.uploadToContext(files, contextId)
  }, [fileOps, contextId])
  
  const listContextFiles = useCallback(async (subPath: string = '') => {
    // Construct context path - this would need to be resolved from contextId
    // For now, using a placeholder pattern
    const contextPath = `/contexts/${contextId}${subPath ? '/' + subPath : ''}`
    return fileOps.listFiles(contextPath, true)
  }, [fileOps, contextId])
  
  return {
    ...fileOps,
    uploadToThisContext,
    listContextFiles
  }
}

/**
 * Hook for file validation
 */
export const useFileValidation = () => {
  const validateFiles = useCallback((files: File[]) => {
    const errors: Array<{ filename: string; message: string }> = []
    const maxSize = 100 * 1024 * 1024 // 100MB
    const allowedTypes = ['image/*', 'text/*', 'application/pdf', 'application/json']
    
    for (const file of files) {
      // Size validation
      if (file.size > maxSize) {
        errors.push({
          filename: file.name,
          message: `File size exceeds ${maxSize / 1024 / 1024}MB limit`
        })
      }
      
      // Type validation
      const isAllowed = allowedTypes.some(type => {
        if (type.endsWith('/*')) {
          return file.type.startsWith(type.slice(0, -2))
        }
        return file.type === type
      })
      
      if (!isAllowed) {
        errors.push({
          filename: file.name,
          message: `File type ${file.type} is not supported`
        })
      }
      
      // Name validation
      const invalidChars = /[<>:"/\\|?*]/
      if (invalidChars.test(file.name) || file.name.length === 0 || file.name.length > 255) {
        errors.push({
          filename: file.name,
          message: 'Filename contains invalid characters or is too long'
        })
      }
    }
    
    return {
      valid: errors.length === 0,
      errors
    }
  }, [])
  
  return { validateFiles }
}
