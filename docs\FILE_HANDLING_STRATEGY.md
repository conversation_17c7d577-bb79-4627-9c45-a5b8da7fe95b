# ChatLo File Handling Strategy

## Overview

This document defines the standardized file handling architecture for ChatLo, establishing clear patterns for file operations, endpoint exposure, and plugin integration. The strategy emphasizes simplicity, security, and extensibility through a unified approach.

## Core Principles

1. **Unified Interface**: All file operations go through a single, consistent API
2. **Plugin-First**: File processing is handled by extensible plugins
3. **Security by Design**: Token-based authentication with proper validation
4. **Local-First**: Optimized for local file system operations
5. **Developer-Friendly**: Clear, predictable endpoints with comprehensive examples

## Architecture Overview

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   UI Drop Zone  │───▶│  FileRouter      │───▶│  Plugin System  │
└─────────────────┘    │  Service         │    └─────────────────┘
                       └──────────────────┘             │
                                │                       ▼
                       ┌──────────────────┐    ┌─────────────────┐
                       │  IPC Gateway     │◀───│  File Processor │
                       └──────────────────┘    │  Plugins        │
                                │              └─────────────────┘
                                ▼
                       ┌──────────────────┐
                       │  File System     │
                       └──────────────────┘
```

## File Routing Strategy

### 1. Automatic File Type Routing

Files are automatically routed based on MIME type and extension:

- **Images** (`image/*`): → `/images/` folder
- **Documents** (`text/*`, `application/pdf`, etc.): → `/documents/` folder
- **Code Files** (`text/javascript`, `application/json`, etc.): → `/code/` folder
- **Archives** (`application/zip`, etc.): → `/archives/` folder

### 2. Context-Aware Routing

Files dropped on specific contexts are routed to:
```
/{contextId}/{fileType}/{filename}
```

Example:
```
/project-alpha/documents/requirements.pdf
/project-alpha/images/mockup.png
```

## Standardized API Endpoints

### Core File Operations

#### 1. File Upload
```typescript
// Endpoint: files:upload
interface FileUploadRequest {
  token: string
  files: File[]
  destination: {
    contextId?: string
    path?: string
    autoRoute?: boolean // Default: true
  }
  options?: {
    overwrite?: boolean
    generateThumbnails?: boolean
    extractText?: boolean
  }
}

interface FileUploadResponse {
  success: boolean
  files: UploadedFile[]
  errors?: FileError[]
}
```

#### 2. File Read
```typescript
// Endpoint: files:read
interface FileReadRequest {
  token: string
  filePath: string
  options?: {
    encoding?: string
    range?: { start: number; end: number }
  }
}

interface FileReadResponse {
  success: boolean
  content: string | Buffer
  metadata: FileMetadata
}
```

#### 3. File Delete
```typescript
// Endpoint: files:delete
interface FileDeleteRequest {
  token: string
  filePaths: string[]
  options?: {
    moveToTrash?: boolean
  }
}
```

#### 4. File List
```typescript
// Endpoint: files:list
interface FileListRequest {
  token: string
  path: string
  options?: {
    recursive?: boolean
    includeHidden?: boolean
    filter?: FileFilter
  }
}

interface FileListResponse {
  success: boolean
  files: FileInfo[]
  totalCount: number
}
```

### Plugin-Specific Endpoints

#### File Processing
```typescript
// Endpoint: files:process
interface FileProcessRequest {
  token: string
  filePath: string
  processor: string // Plugin ID
  options?: Record<string, any>
}
```

#### Batch Operations
```typescript
// Endpoint: files:batch
interface BatchOperationRequest {
  token: string
  operations: FileOperation[]
}

interface FileOperation {
  type: 'upload' | 'delete' | 'move' | 'copy' | 'process'
  source?: string
  destination?: string
  options?: Record<string, any>
}
```

## Implementation Examples

### 1. Frontend File Drop Handler

```typescript
// src/services/fileRoutingService.ts
export class FileRoutingService {
  private apiClient: APIClient
  
  constructor(apiClient: APIClient) {
    this.apiClient = apiClient
  }

  async handleFileDrop(
    files: File[], 
    destination: FileDestination
  ): Promise<FileUploadResponse> {
    const token = await this.apiClient.getToken('files.write')
    
    return await this.apiClient.call('files:upload', {
      token,
      files,
      destination: {
        contextId: destination.contextId,
        autoRoute: true
      },
      options: {
        generateThumbnails: true,
        extractText: true
      }
    })
  }

  async uploadToContext(
    files: File[], 
    contextId: string
  ): Promise<FileUploadResponse> {
    return this.handleFileDrop(files, { contextId })
  }
}
```

### 2. Unified Drop Zone Component

```typescript
// src/components/UnifiedDropZone.tsx
interface UnifiedDropZoneProps {
  contextId?: string
  path?: string
  onUploadComplete?: (files: UploadedFile[]) => void
  onUploadError?: (error: string) => void
}

export const UnifiedDropZone: React.FC<UnifiedDropZoneProps> = ({
  contextId,
  path,
  onUploadComplete,
  onUploadError
}) => {
  const fileRouter = useFileRouter()
  
  const handleDrop = async (e: React.DragEvent) => {
    e.preventDefault()
    const files = Array.from(e.dataTransfer.files)
    
    try {
      const result = await fileRouter.handleFileDrop(files, {
        contextId,
        path
      })
      
      if (result.success) {
        onUploadComplete?.(result.files)
      } else {
        onUploadError?.(result.errors?.[0]?.message || 'Upload failed')
      }
    } catch (error) {
      onUploadError?.(error.message)
    }
  }

  return (
    <div
      className="drop-zone"
      onDrop={handleDrop}
      onDragOver={(e) => e.preventDefault()}
    >
      <p>Drop files here</p>
    </div>
  )
}
```

### 3. Backend IPC Handler

```typescript
// electron/api/fileHandlers.ts
export class FileHandlers {
  private pluginManager: PluginManager
  private securityManager: SecurityManager
  
  registerEndpoints(apiRegistry: APIRegistry) {
    // File upload endpoint
    apiRegistry.registerEndpoint('files', 'upload', 
      this.handleFileUpload.bind(this), {
        validator: this.validateFileUpload.bind(this),
        middleware: [
          this.securityManager.validateToken,
          this.securityManager.checkPermission('files.write')
        ],
        description: 'Upload files to the vault'
      }
    )

    // File read endpoint
    apiRegistry.registerEndpoint('files', 'read',
      this.handleFileRead.bind(this), {
        validator: this.validateFileRead.bind(this),
        middleware: [
          this.securityManager.validateToken,
          this.securityManager.checkPermission('files.read')
        ],
        description: 'Read file content'
      }
    )
  }

  private async handleFileUpload(request: FileUploadRequest): Promise<FileUploadResponse> {
    const results: UploadedFile[] = []
    const errors: FileError[] = []

    for (const file of request.files) {
      try {
        // Determine destination path
        const destinationPath = this.resolveDestinationPath(file, request.destination)
        
        // Get appropriate processor plugin
        const processor = this.pluginManager.getFileProcessor(file.type)
        
        // Process and save file
        const result = await processor.processFile(file, {
          destinationPath,
          options: request.options
        })
        
        results.push(result)
      } catch (error) {
        errors.push({
          filename: file.name,
          message: error.message
        })
      }
    }

    return {
      success: errors.length === 0,
      files: results,
      errors: errors.length > 0 ? errors : undefined
    }
  }

  private resolveDestinationPath(file: File, destination: FileDestination): string {
    if (destination.path) {
      return destination.path
    }

    const baseDir = destination.contextId 
      ? `/contexts/${destination.contextId}` 
      : '/shared'
    
    if (destination.autoRoute !== false) {
      const folder = this.getAutoRouteFolder(file.type)
      return `${baseDir}/${folder}/${file.name}`
    }

    return `${baseDir}/${file.name}`
  }

  private getAutoRouteFolder(mimeType: string): string {
    if (mimeType.startsWith('image/')) return 'images'
    if (mimeType.startsWith('text/') || mimeType === 'application/pdf') return 'documents'
    if (mimeType.includes('javascript') || mimeType.includes('json')) return 'code'
    if (mimeType.includes('zip') || mimeType.includes('archive')) return 'archives'
    return 'documents'
  }
}
```

## Plugin Integration

### File Processor Plugin Interface

```typescript
// electron/plugins/types/fileProcessor.ts
export interface FileProcessorPlugin extends BasePlugin {
  // Supported file types
  supportedMimeTypes: string[]
  supportedExtensions: string[]
  
  // Process file
  processFile(file: File, options: ProcessingOptions): Promise<ProcessedFile>
  
  // Generate metadata
  extractMetadata?(file: File): Promise<FileMetadata>
  
  // Generate thumbnails (for images/videos)
  generateThumbnail?(file: File): Promise<Buffer>
}

export interface ProcessingOptions {
  destinationPath: string
  generateThumbnails?: boolean
  extractText?: boolean
  customOptions?: Record<string, any>
}

export interface ProcessedFile {
  originalName: string
  savedPath: string
  size: number
  mimeType: string
  metadata?: FileMetadata
  thumbnail?: string // Base64 or path
  extractedText?: string
}
```

### Example Image Processor Plugin

```typescript
// electron/plugins/core/imageProcessor.ts
export class ImageProcessorPlugin implements FileProcessorPlugin {
  id = 'core-image-processor'
  name = 'Core Image Processor'
  supportedMimeTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
  supportedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp']

  async processFile(file: File, options: ProcessingOptions): Promise<ProcessedFile> {
    // Save original file
    const savedPath = await this.saveFile(file, options.destinationPath)
    
    // Extract metadata
    const metadata = await this.extractMetadata(file)
    
    // Generate thumbnail if requested
    let thumbnail: string | undefined
    if (options.generateThumbnails) {
      const thumbnailBuffer = await this.generateThumbnail(file)
      thumbnail = thumbnailBuffer.toString('base64')
    }

    return {
      originalName: file.name,
      savedPath,
      size: file.size,
      mimeType: file.type,
      metadata,
      thumbnail
    }
  }

  async extractMetadata(file: File): Promise<FileMetadata> {
    // Use sharp or similar library to extract EXIF data
    const buffer = await file.arrayBuffer()
    // ... metadata extraction logic
    return {
      width: 1920,
      height: 1080,
      format: 'JPEG',
      // ... other metadata
    }
  }

  async generateThumbnail(file: File): Promise<Buffer> {
    // Generate 200x200 thumbnail
    const buffer = await file.arrayBuffer()
    // ... thumbnail generation logic
    return thumbnailBuffer
  }
}
```

## Security Implementation

### Token-Based Authentication

```typescript
// electron/api/security/tokenManager.ts
export class TokenManager {
  private tokens: Map<string, TokenInfo> = new Map()
  
  async generateToken(permissions: string[]): Promise<string> {
    const token = jwt.sign({
      permissions,
      iat: Date.now(),
      exp: Date.now() + (24 * 60 * 60 * 1000) // 24 hours
    }, this.getSecretKey())
    
    return token
  }
  
  async validateToken(token: string): Promise<TokenInfo> {
    try {
      const payload = jwt.verify(token, this.getSecretKey())
      return new TokenInfo(payload)
    } catch (error) {
      throw new UnauthorizedError('Invalid token')
    }
  }
}
```

### Permission System

```typescript
// electron/api/security/permissions.ts
export const FILE_PERMISSIONS = {
  READ: 'files.read',
  WRITE: 'files.write',
  DELETE: 'files.delete',
  PROCESS: 'files.process'
} as const

export class PermissionChecker {
  checkPermission(tokenInfo: TokenInfo, requiredPermission: string): boolean {
    return tokenInfo.permissions.includes(requiredPermission)
  }
}
```

## Migration Strategy

### Phase 1: Core Infrastructure
1. Implement `FileRoutingService`
2. Create unified IPC endpoints
3. Add basic token authentication

### Phase 2: Plugin System
1. Implement `FileProcessorPlugin` interface
2. Create core processor plugins (Image, Document, Text)
3. Migrate existing file handling to plugins

### Phase 3: UI Integration
1. Replace existing drop zones with `UnifiedDropZone`
2. Update all file operations to use new API
3. Add progress indicators and error handling

### Phase 4: Advanced Features
1. Add batch operations
2. Implement file versioning
3. Add advanced security features

## Benefits

1. **Consistency**: All file operations follow the same patterns
2. **Extensibility**: Easy to add new file types and processors
3. **Security**: Proper authentication and authorization
4. **Performance**: Optimized for local operations
5. **Developer Experience**: Clear APIs with comprehensive examples
6. **Future-Proof**: Plugin architecture supports new requirements

This strategy provides a solid foundation for ChatLo's file handling while maintaining flexibility for future enhancements and third-party integrations.

## Complete Implementation Reference

### Frontend API Client

```typescript
// src/api/fileClient.ts
export class FileAPIClient {
  private baseURL: string
  private tokenManager: TokenManager

  constructor(baseURL: string, tokenManager: TokenManager) {
    this.baseURL = baseURL
    this.tokenManager = tokenManager
  }

  async uploadFiles(files: File[], destination: FileDestination): Promise<FileUploadResponse> {
    const token = await this.tokenManager.getToken(['files.write'])

    const formData = new FormData()
    files.forEach((file, index) => {
      formData.append(`file_${index}`, file)
    })
    formData.append('token', token)
    formData.append('destination', JSON.stringify(destination))

    const response = await window.electronAPI.files.upload({
      token,
      files,
      destination
    })

    return response
  }

  async readFile(filePath: string): Promise<FileReadResponse> {
    const token = await this.tokenManager.getToken(['files.read'])

    return await window.electronAPI.files.read({
      token,
      filePath
    })
  }

  async listFiles(path: string, options?: FileListOptions): Promise<FileListResponse> {
    const token = await this.tokenManager.getToken(['files.read'])

    return await window.electronAPI.files.list({
      token,
      path,
      options
    })
  }

  async deleteFiles(filePaths: string[]): Promise<FileDeleteResponse> {
    const token = await this.tokenManager.getToken(['files.delete'])

    return await window.electronAPI.files.delete({
      token,
      filePaths
    })
  }
}
```

### React Hook for File Operations

```typescript
// src/hooks/useFileOperations.ts
export const useFileOperations = () => {
  const [uploading, setUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>({})
  const fileClient = useFileClient()

  const uploadFiles = useCallback(async (
    files: File[],
    destination: FileDestination,
    onProgress?: (progress: number) => void
  ) => {
    setUploading(true)
    try {
      const result = await fileClient.uploadFiles(files, destination)

      if (result.success) {
        // Trigger UI refresh
        await refreshFileList()
        return result.files
      } else {
        throw new Error(result.errors?.[0]?.message || 'Upload failed')
      }
    } finally {
      setUploading(false)
    }
  }, [fileClient])

  const deleteFiles = useCallback(async (filePaths: string[]) => {
    const result = await fileClient.deleteFiles(filePaths)
    if (result.success) {
      await refreshFileList()
    }
    return result
  }, [fileClient])

  return {
    uploadFiles,
    deleteFiles,
    uploading,
    uploadProgress
  }
}
```

### Context-Aware File Management

```typescript
// src/services/contextFileManager.ts
export class ContextFileManager {
  private fileClient: FileAPIClient
  private contextService: ContextVaultService

  constructor(fileClient: FileAPIClient, contextService: ContextVaultService) {
    this.fileClient = fileClient
    this.contextService = contextService
  }

  async addFilesToContext(files: File[], contextId: string): Promise<UploadedFile[]> {
    const context = await this.contextService.getContext(contextId)
    if (!context) {
      throw new Error(`Context ${contextId} not found`)
    }

    const destination: FileDestination = {
      contextId,
      autoRoute: true
    }

    const result = await this.fileClient.uploadFiles(files, destination)

    if (result.success) {
      // Update context metadata
      await this.updateContextFileCount(contextId, files.length)
      return result.files
    } else {
      throw new Error(`Failed to upload files: ${result.errors?.[0]?.message}`)
    }
  }

  async getContextFiles(contextId: string): Promise<FileInfo[]> {
    const contextPath = `/contexts/${contextId}`
    const result = await this.fileClient.listFiles(contextPath, {
      recursive: true
    })

    if (result.success) {
      return result.files
    } else {
      throw new Error('Failed to list context files')
    }
  }

  private async updateContextFileCount(contextId: string, additionalFiles: number): Promise<void> {
    // Update context metadata with new file count
    await this.contextService.updateContext(contextId, {
      fileCount: (await this.getContextFiles(contextId)).length
    })
  }
}
```

### Error Handling and Validation

```typescript
// src/utils/fileValidation.ts
export class FileValidator {
  private static readonly MAX_FILE_SIZE = 100 * 1024 * 1024 // 100MB
  private static readonly ALLOWED_TYPES = [
    'image/*',
    'text/*',
    'application/pdf',
    'application/json',
    'application/zip'
  ]

  static validateFiles(files: File[]): ValidationResult {
    const errors: FileValidationError[] = []

    for (const file of files) {
      // Size validation
      if (file.size > this.MAX_FILE_SIZE) {
        errors.push({
          filename: file.name,
          error: 'FILE_TOO_LARGE',
          message: `File size exceeds ${this.MAX_FILE_SIZE / 1024 / 1024}MB limit`
        })
      }

      // Type validation
      if (!this.isAllowedType(file.type)) {
        errors.push({
          filename: file.name,
          error: 'UNSUPPORTED_TYPE',
          message: `File type ${file.type} is not supported`
        })
      }

      // Name validation
      if (!this.isValidFilename(file.name)) {
        errors.push({
          filename: file.name,
          error: 'INVALID_FILENAME',
          message: 'Filename contains invalid characters'
        })
      }
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }

  private static isAllowedType(mimeType: string): boolean {
    return this.ALLOWED_TYPES.some(allowedType => {
      if (allowedType.endsWith('/*')) {
        const prefix = allowedType.slice(0, -2)
        return mimeType.startsWith(prefix)
      }
      return mimeType === allowedType
    })
  }

  private static isValidFilename(filename: string): boolean {
    // Check for invalid characters
    const invalidChars = /[<>:"/\\|?*]/
    return !invalidChars.test(filename) && filename.length > 0 && filename.length <= 255
  }
}
```

### Plugin Registration System

```typescript
// electron/plugins/pluginRegistry.ts
export class FileProcessorRegistry {
  private processors: Map<string, FileProcessorPlugin> = new Map()
  private typeMap: Map<string, string> = new Map() // mimeType -> pluginId

  registerProcessor(plugin: FileProcessorPlugin): void {
    this.processors.set(plugin.id, plugin)

    // Map supported types to this plugin
    plugin.supportedMimeTypes.forEach(type => {
      this.typeMap.set(type, plugin.id)
    })

    console.log(`Registered file processor: ${plugin.name} (${plugin.id})`)
  }

  getProcessor(mimeType: string): FileProcessorPlugin {
    const pluginId = this.typeMap.get(mimeType)
    if (!pluginId) {
      // Fall back to default processor
      return this.processors.get('core-default-processor')!
    }

    const processor = this.processors.get(pluginId)
    if (!processor) {
      throw new Error(`File processor ${pluginId} not found`)
    }

    return processor
  }

  getAllProcessors(): FileProcessorPlugin[] {
    return Array.from(this.processors.values())
  }

  getSupportedTypes(): string[] {
    return Array.from(this.typeMap.keys())
  }
}
```

### Monitoring and Analytics

```typescript
// electron/api/monitoring/fileOperationMonitor.ts
export class FileOperationMonitor {
  private metrics: Map<string, OperationMetrics> = new Map()

  recordOperation(operation: FileOperation): void {
    const key = `${operation.type}_${operation.contextId || 'global'}`
    const existing = this.metrics.get(key) || {
      count: 0,
      totalSize: 0,
      averageTime: 0,
      errors: 0
    }

    existing.count++
    existing.totalSize += operation.fileSize || 0
    existing.averageTime = (existing.averageTime * (existing.count - 1) + operation.duration) / existing.count

    if (operation.error) {
      existing.errors++
    }

    this.metrics.set(key, existing)
  }

  getMetrics(): Record<string, OperationMetrics> {
    return Object.fromEntries(this.metrics)
  }

  getUsageStats(): UsageStats {
    let totalOperations = 0
    let totalSize = 0
    let totalErrors = 0

    for (const metrics of this.metrics.values()) {
      totalOperations += metrics.count
      totalSize += metrics.totalSize
      totalErrors += metrics.errors
    }

    return {
      totalOperations,
      totalSize,
      totalErrors,
      errorRate: totalOperations > 0 ? totalErrors / totalOperations : 0
    }
  }
}
```

### Configuration Management

```typescript
// electron/config/fileHandlingConfig.ts
export interface FileHandlingConfig {
  maxFileSize: number
  allowedTypes: string[]
  autoRouting: {
    enabled: boolean
    rules: RoutingRule[]
  }
  thumbnails: {
    enabled: boolean
    maxSize: { width: number; height: number }
    quality: number
  }
  textExtraction: {
    enabled: boolean
    maxFileSize: number
  }
  security: {
    scanFiles: boolean
    quarantineUnsafe: boolean
  }
}

export const DEFAULT_CONFIG: FileHandlingConfig = {
  maxFileSize: 100 * 1024 * 1024, // 100MB
  allowedTypes: ['image/*', 'text/*', 'application/pdf'],
  autoRouting: {
    enabled: true,
    rules: [
      { pattern: 'image/*', destination: 'images' },
      { pattern: 'text/*', destination: 'documents' },
      { pattern: 'application/pdf', destination: 'documents' }
    ]
  },
  thumbnails: {
    enabled: true,
    maxSize: { width: 200, height: 200 },
    quality: 80
  },
  textExtraction: {
    enabled: true,
    maxFileSize: 10 * 1024 * 1024 // 10MB
  },
  security: {
    scanFiles: false,
    quarantineUnsafe: false
  }
}
```

This comprehensive implementation provides all the necessary components for a robust, secure, and extensible file handling system in ChatLo.
