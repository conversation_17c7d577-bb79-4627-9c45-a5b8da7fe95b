import React, { useState } from 'react'
import { AlertCircle, CheckCircle, Clock, FileText, X } from './Icons'

interface DiagnosticResult {
  step: string
  status: 'success' | 'warning' | 'error'
  message: string
  details?: any
  timestamp: string
}

interface FileProcessingDiagnostic {
  filePath: string
  filename: string
  fileSize: number
  fileType: string
  mimeType: string
  results: DiagnosticResult[]
  summary: {
    totalSteps: number
    successCount: number
    warningCount: number
    errorCount: number
    overallStatus: 'success' | 'warning' | 'error'
  }
}

interface FileProcessingDiagnosticsProps {
  isOpen: boolean
  onClose: () => void
}

const FileProcessingDiagnostics: React.FC<FileProcessingDiagnosticsProps> = ({ isOpen, onClose }) => {
  const [selectedFile, setSelectedFile] = useState<string>('')
  const [isRunning, setIsRunning] = useState(false)
  const [diagnostic, setDiagnostic] = useState<FileProcessingDiagnostic | null>(null)
  const [expandedSteps, setExpandedSteps] = useState<Set<string>>(new Set())

  if (!isOpen) return null

  const handleSelectFile = async () => {
    try {
      if (window.electronAPI?.files) {
        const result = await window.electronAPI.files.showOpenDialog({
          title: 'Select File for Diagnostics',
          properties: ['openFile'],
          filters: [
            { name: 'PDF Files', extensions: ['pdf'] },
            { name: 'Documents', extensions: ['doc', 'docx', 'txt', 'md'] },
            { name: 'All Files', extensions: ['*'] }
          ]
        })

        if (!result.canceled && result.filePaths.length > 0) {
          setSelectedFile(result.filePaths[0])
        }
      }
    } catch (error) {
      console.error('Error selecting file:', error)
    }
  }

  const runDiagnostics = async () => {
    if (!selectedFile) return

    setIsRunning(true)
    setDiagnostic(null)

    try {
      if (window.electronAPI?.files) {
        const result = await window.electronAPI.files.diagnoseFileProcessing(selectedFile)
        setDiagnostic(result)
      }
    } catch (error) {
      console.error('Error running diagnostics:', error)
    } finally {
      setIsRunning(false)
    }
  }

  const toggleStepExpansion = (step: string) => {
    const newExpanded = new Set(expandedSteps)
    if (newExpanded.has(step)) {
      newExpanded.delete(step)
    } else {
      newExpanded.add(step)
    }
    setExpandedSteps(newExpanded)
  }

  const getStatusIcon = (status: 'success' | 'warning' | 'error') => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-400" />
      case 'warning':
        return <AlertCircle className="w-5 h-5 text-yellow-400" />
      case 'error':
        return <AlertCircle className="w-5 h-5 text-red-400" />
    }
  }

  const getStatusColor = (status: 'success' | 'warning' | 'error') => {
    switch (status) {
      case 'success':
        return 'border-green-400 bg-green-400/10'
      case 'warning':
        return 'border-yellow-400 bg-yellow-400/10'
      case 'error':
        return 'border-red-400 bg-red-400/10'
    }
  }

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-neutral-800 rounded-lg border border-neutral-600 w-full max-w-4xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-neutral-600">
          <div className="flex items-center gap-3">
            <FileText className="w-6 h-6 text-blue-400" />
            <h2 className="text-lg font-semibold text-white">File Processing Diagnostics</h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-neutral-700 rounded-lg transition-colors"
          >
            <X className="w-5 h-5 text-neutral-400" />
          </button>
        </div>

        <div className="p-4 overflow-y-auto max-h-[calc(90vh-80px)]">
          {/* File Selection */}
          <div className="mb-6">
            <h3 className="text-sm font-medium text-neutral-300 mb-3">Select File to Diagnose</h3>
            <div className="flex gap-3">
              <button
                onClick={handleSelectFile}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
              >
                Browse Files
              </button>
              {selectedFile && (
                <div className="flex-1 px-3 py-2 bg-neutral-700 rounded-lg text-sm text-neutral-300 truncate">
                  {selectedFile}
                </div>
              )}
            </div>
          </div>

          {/* Run Diagnostics Button */}
          {selectedFile && (
            <div className="mb-6">
              <button
                onClick={runDiagnostics}
                disabled={isRunning}
                className="px-6 py-2 bg-green-600 hover:bg-green-700 disabled:bg-neutral-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors flex items-center gap-2"
              >
                {isRunning ? (
                  <>
                    <Clock className="w-4 h-4 animate-spin" />
                    Running Diagnostics...
                  </>
                ) : (
                  'Run Diagnostics'
                )}
              </button>
            </div>
          )}

          {/* Diagnostic Results */}
          {diagnostic && (
            <div className="space-y-6">
              {/* Summary */}
              <div className={`p-4 rounded-lg border ${getStatusColor(diagnostic.summary.overallStatus)}`}>
                <div className="flex items-center gap-3 mb-3">
                  {getStatusIcon(diagnostic.summary.overallStatus)}
                  <h3 className="text-lg font-semibold text-white">Diagnostic Summary</h3>
                </div>
                
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-white">{diagnostic.summary.totalSteps}</div>
                    <div className="text-sm text-neutral-400">Total Steps</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-400">{diagnostic.summary.successCount}</div>
                    <div className="text-sm text-neutral-400">Success</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-yellow-400">{diagnostic.summary.warningCount}</div>
                    <div className="text-sm text-neutral-400">Warnings</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-400">{diagnostic.summary.errorCount}</div>
                    <div className="text-sm text-neutral-400">Errors</div>
                  </div>
                </div>

                <div className="text-sm text-neutral-300">
                  <div><strong>File:</strong> {diagnostic.filename}</div>
                  <div><strong>Size:</strong> {formatFileSize(diagnostic.fileSize)}</div>
                  <div><strong>Type:</strong> {diagnostic.fileType}</div>
                  <div><strong>MIME:</strong> {diagnostic.mimeType}</div>
                </div>
              </div>

              {/* Detailed Results */}
              <div className="space-y-3">
                <h3 className="text-lg font-semibold text-white">Detailed Results</h3>
                {diagnostic.results.map((result, index) => (
                  <div
                    key={index}
                    className={`border rounded-lg ${getStatusColor(result.status)}`}
                  >
                    <button
                      onClick={() => toggleStepExpansion(result.step)}
                      className="w-full p-4 text-left hover:bg-white/5 transition-colors"
                    >
                      <div className="flex items-center gap-3">
                        {getStatusIcon(result.status)}
                        <div className="flex-1">
                          <div className="font-medium text-white">{result.step}</div>
                          <div className="text-sm text-neutral-300">{result.message}</div>
                        </div>
                        <div className="text-xs text-neutral-400">
                          {new Date(result.timestamp).toLocaleTimeString()}
                        </div>
                      </div>
                    </button>
                    
                    {expandedSteps.has(result.step) && result.details && (
                      <div className="px-4 pb-4">
                        <div className="bg-neutral-900/50 rounded p-3">
                          <pre className="text-xs text-neutral-300 whitespace-pre-wrap overflow-x-auto">
                            {JSON.stringify(result.details, null, 2)}
                          </pre>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default FileProcessingDiagnostics
