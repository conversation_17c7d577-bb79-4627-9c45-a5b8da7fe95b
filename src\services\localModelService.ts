import { BaseService, ServiceError, ServiceErrorCode } from './base'

export interface LocalModel {
  id: string
  name: string
  provider: 'ollama' | 'lmstudio'
  size?: string
  modified?: string
  digest?: string
  details?: {
    format?: string
    family?: string
    families?: string[]
    parameter_size?: string
    quantization_level?: string
  }
}

export interface LocalModelProvider {
  name: string
  baseUrl: string
  isConnected: boolean
  models: LocalModel[]
}

class LocalModelService extends BaseService {
  private ollamaBaseUrl = 'http://localhost:11434'
  private lmStudioBaseUrl = 'http://localhost:1234'

  constructor() {
    super({
      name: 'LocalModelService',
      autoInitialize: true
    })
  }

  /**
   * Initialize the service
   */
  protected async doInitialize(): Promise<void> {
    this.logger.info('Local model service initialized', 'doInitialize', {
      ollamaUrl: this.ollamaBaseUrl,
      lmStudioUrl: this.lmStudioBaseUrl
    })
  }

  /**
   * Health check implementation - check if at least one provider is available
   */
  protected async doHealthCheck(): Promise<boolean> {
    try {
      const [ollamaResult, lmStudioResult] = await Promise.all([
        this.checkOllama(),
        this.checkLMStudio()
      ])

      const isHealthy = ollamaResult.connected || lmStudioResult.connected
      this.logger.info('Health check completed', 'doHealthCheck', {
        ollamaConnected: ollamaResult.connected,
        lmStudioConnected: lmStudioResult.connected,
        isHealthy
      })

      return isHealthy
    } catch (error) {
      this.logger.warn('Health check failed', 'doHealthCheck', error)
      return false
    }
  }

  /**
   * Cleanup implementation
   */
  protected async doCleanup(): Promise<void> {
    this.logger.info('Local model service cleaned up', 'doCleanup')
  }

  // Check if Ollama is available and get models
  async checkOllama(): Promise<{ connected: boolean; models: LocalModel[] }> {
    const result = await this.executeOperation(
      'checkOllama',
      async () => {
        this.logger.debug('Checking Ollama availability', 'checkOllama', { url: this.ollamaBaseUrl })

        const response = await fetch(`${this.ollamaBaseUrl}/api/tags`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json; charset=utf-8',
            'Accept': 'application/json; charset=utf-8'
          },
          signal: AbortSignal.timeout(5000)
        })

        if (!response.ok) {
          throw new ServiceError(
            ServiceErrorCode.EXTERNAL_SERVICE_ERROR,
            `Ollama API error: ${response.status} ${response.statusText}`,
            { serviceName: this.serviceName, operation: 'checkOllama', details: { status: response.status } }
          )
        }

        const data = await response.json()
        const models: LocalModel[] = data.models?.map((model: any) => ({
          id: `ollama:${model.name}`,
          name: model.name,
          provider: 'ollama' as const,
          size: model.size ? this.formatBytes(model.size) : undefined,
          modified: model.modified_at,
          digest: model.digest,
          details: model.details
        })) || []

        this.logger.info('Ollama connected successfully', 'checkOllama', {
          modelCount: models.length,
          models: models.map(m => m.name)
        })

        return { connected: true, models }
      }
    )

    if (!result.success) {
      this.logger.warn('Ollama not available', 'checkOllama', result.error)
      return { connected: false, models: [] }
    }

    return result.data!
  }

  // Check if LM Studio is available and get models
  async checkLMStudio(): Promise<{ connected: boolean; models: LocalModel[] }> {
    const result = await this.executeOperation(
      'checkLMStudio',
      async () => {
        this.logger.debug('Checking LM Studio availability', 'checkLMStudio', { url: this.lmStudioBaseUrl })

        const response = await fetch(`${this.lmStudioBaseUrl}/v1/models`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json; charset=utf-8',
            'Accept': 'application/json; charset=utf-8'
          },
          signal: AbortSignal.timeout(5000)
        })

        if (!response.ok) {
          throw new ServiceError(
            ServiceErrorCode.EXTERNAL_SERVICE_ERROR,
            `LM Studio API error: ${response.status} ${response.statusText}`,
            { serviceName: this.serviceName, operation: 'checkLMStudio', details: { status: response.status } }
          )
        }

        const data = await response.json()
        const models: LocalModel[] = data.data?.map((model: any) => ({
          id: `lmstudio:${model.id}`,
          name: model.id,
          provider: 'lmstudio' as const,
          size: undefined, // LM Studio doesn't provide size info
          modified: undefined,
          digest: undefined,
          details: undefined
        })) || []

        this.logger.info('LM Studio connected successfully', 'checkLMStudio', {
          modelCount: models.length,
          models: models.map(m => m.name)
        })

        return { connected: true, models }
      }
    )

    if (!result.success) {
      this.logger.warn('LM Studio not available', 'checkLMStudio', result.error)
      return { connected: false, models: [] }
    }

    return result.data!
  }

  // Get all available local models
  async getAllLocalModels(): Promise<LocalModel[]> {
    const [ollamaResult, lmStudioResult] = await Promise.all([
      this.checkOllama(),
      this.checkLMStudio()
    ])

    return [...ollamaResult.models, ...lmStudioResult.models]
  }

  // Get provider status
  async getProviderStatus(): Promise<{
    ollama: LocalModelProvider
    lmstudio: LocalModelProvider
  }> {
    const [ollamaResult, lmStudioResult] = await Promise.all([
      this.checkOllama(),
      this.checkLMStudio()
    ])

    return {
      ollama: {
        name: 'Ollama',
        baseUrl: this.ollamaBaseUrl,
        isConnected: ollamaResult.connected,
        models: ollamaResult.models
      },
      lmstudio: {
        name: 'LM Studio',
        baseUrl: this.lmStudioBaseUrl,
        isConnected: lmStudioResult.connected,
        models: lmStudioResult.models
      }
    }
  }

  // Send message to local model
  async sendMessage(
    modelId: string,
    messages: Array<{ role: string; content: string }>,
    onChunk?: (chunk: string) => void
  ): Promise<string> {
    return await this.executeOperationOrThrow(
      'sendMessage',
      async () => {
        const [provider, modelName] = modelId.split(':')

        if (provider === 'ollama') {
          return await this.sendOllamaMessage(modelName, messages, onChunk)
        } else if (provider === 'lmstudio') {
          return await this.sendLMStudioMessage(modelName, messages, onChunk)
        } else {
          throw new ServiceError(
            ServiceErrorCode.VALIDATION_ERROR,
            `Unknown local model provider: ${provider}`,
            { serviceName: this.serviceName, operation: 'sendMessage', details: { modelId, provider } }
          )
        }
      },
      { modelId, messageCount: messages.length, hasChunkCallback: !!onChunk }
    )
  }

  // Send message to Ollama
  private async sendOllamaMessage(
    model: string,
    messages: Array<{ role: string; content: string }>,
    onChunk?: (chunk: string) => void
  ): Promise<string> {
    this.logger.info('Sending message to Ollama', 'sendOllamaMessage', {
      model,
      messageCount: messages.length,
      streaming: !!onChunk
    })

    const response = await fetch(`${this.ollamaBaseUrl}/api/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model,
        messages,
        stream: !!onChunk
      })
    })

    if (!response.ok) {
      throw new ServiceError(
        ServiceErrorCode.EXTERNAL_SERVICE_ERROR,
        `Ollama API error: ${response.status} ${response.statusText}`,
        { serviceName: this.serviceName, operation: 'sendOllamaMessage', details: { model, status: response.status } }
      )
    }

    if (onChunk && response.body) {
      const reader = response.body.getReader()
      const decoder = new TextDecoder()
      let fullResponse = ''
      let chunkCount = 0

      try {
        while (true) {
          const { done, value } = await reader.read()
          if (done) break

          const chunk = decoder.decode(value)
          const lines = chunk.split('\n').filter(line => line.trim())

          for (const line of lines) {
            try {
              const data = JSON.parse(line)
              if (data.message?.content) {
                fullResponse += data.message.content
                onChunk(data.message.content)
                chunkCount++
              }
            } catch (e) {
              // Ignore malformed JSON
              this.logger.debug('Failed to parse Ollama streaming chunk', 'sendOllamaMessage', e)
            }
          }
        }
      } catch (error) {
        throw new ServiceError(
          ServiceErrorCode.EXTERNAL_SERVICE_ERROR,
          `Ollama streaming error: ${error instanceof Error ? error.message : 'Unknown error'}`,
          { serviceName: this.serviceName, operation: 'sendOllamaMessage', details: { model, chunkCount } }
        )
      } finally {
        reader.releaseLock()
      }

      this.logger.info('Ollama streaming completed', 'sendOllamaMessage', {
        model,
        responseLength: fullResponse.length,
        chunkCount
      })

      return fullResponse
    } else {
      const data = await response.json()
      const content = data.message?.content || ''

      this.logger.info('Ollama response received', 'sendOllamaMessage', {
        model,
        responseLength: content.length
      })

      return content
    }
  }

  // Send message to LM Studio
  private async sendLMStudioMessage(
    model: string,
    messages: Array<{ role: string; content: string }>,
    onChunk?: (chunk: string) => void
  ): Promise<string> {
    this.logger.info('Sending message to LM Studio', 'sendLMStudioMessage', {
      model,
      messageCount: messages.length,
      streaming: !!onChunk
    })

    const response = await fetch(`${this.lmStudioBaseUrl}/v1/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model,
        messages,
        stream: !!onChunk
      })
    })

    if (!response.ok) {
      throw new ServiceError(
        ServiceErrorCode.EXTERNAL_SERVICE_ERROR,
        `LM Studio API error: ${response.status} ${response.statusText}`,
        { serviceName: this.serviceName, operation: 'sendLMStudioMessage', details: { model, status: response.status } }
      )
    }

    if (onChunk && response.body) {
      const reader = response.body.getReader()
      const decoder = new TextDecoder()
      let fullResponse = ''
      let chunkCount = 0

      try {
        while (true) {
          const { done, value } = await reader.read()
          if (done) break

          const chunk = decoder.decode(value)
          const lines = chunk.split('\n').filter(line => line.trim())

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(6)
              if (data === '[DONE]') continue

              try {
                const parsed = JSON.parse(data)
                const content = parsed.choices?.[0]?.delta?.content
                if (content) {
                  fullResponse += content
                  onChunk(content)
                  chunkCount++
                }
              } catch (e) {
                // Ignore malformed JSON
                this.logger.debug('Failed to parse LM Studio streaming chunk', 'sendLMStudioMessage', e)
              }
            }
          }
        }
      } catch (error) {
        throw new ServiceError(
          ServiceErrorCode.EXTERNAL_SERVICE_ERROR,
          `LM Studio streaming error: ${error instanceof Error ? error.message : 'Unknown error'}`,
          { serviceName: this.serviceName, operation: 'sendLMStudioMessage', details: { model, chunkCount } }
        )
      } finally {
        reader.releaseLock()
      }

      this.logger.info('LM Studio streaming completed', 'sendLMStudioMessage', {
        model,
        responseLength: fullResponse.length,
        chunkCount
      })

      return fullResponse
    } else {
      const data = await response.json()
      const content = data.choices?.[0]?.message?.content || ''

      this.logger.info('LM Studio response received', 'sendLMStudioMessage', {
        model,
        responseLength: content.length
      })

      return content
    }
  }

  // Utility function to format bytes
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }
}

export const localModelService = new LocalModelService()
