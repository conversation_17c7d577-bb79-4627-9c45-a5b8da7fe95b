import { PluginFileProcessorService } from '../fileProcessors/PluginFileProcessor'
import * as fs from 'fs'
import * as path from 'path'

export interface ParsingStep {
  step: string
  timestamp: string
  success: boolean
  data?: any
  error?: string
  duration?: number
}

export interface ParsingResult {
  success: boolean
  steps: ParsingStep[]
  finalContent: string | null
  finalError: string | null
  totalDuration: number
  fileInfo: {
    path: string
    filename: string
    size: number
    type: string
    exists: boolean
  }
}

export class ParsingDebugger {
  private fileProcessor: PluginFileProcessorService

  constructor() {
    this.fileProcessor = new PluginFileProcessorService()
  }

  async debugFileParsing(filePath: string): Promise<ParsingResult> {
    const startTime = Date.now()
    const steps: ParsingStep[] = []
    let finalContent: string | null = null
    let finalError: string | null = null

    try {
      // Step 1: File existence check
      const step1Start = Date.now()
      const fileExists = fs.existsSync(filePath)
      steps.push({
        step: 'file_existence_check',
        timestamp: new Date().toISOString(),
        success: fileExists,
        data: { filePath, exists: fileExists },
        error: fileExists ? undefined : 'File does not exist',
        duration: Date.now() - step1Start
      })

      if (!fileExists) {
        return this.createResult(steps, null, 'File does not exist', startTime, filePath)
      }

      // Step 2: File info gathering
      const step2Start = Date.now()
      const stats = fs.statSync(filePath)
      const filename = path.basename(filePath)
      const fileType = this.getFileType(filename)
      
      steps.push({
        step: 'file_info_gathering',
        timestamp: new Date().toISOString(),
        success: true,
        data: {
          filename,
          size: stats.size,
          type: fileType,
          extension: path.extname(filename)
        },
        duration: Date.now() - step2Start
      })

      // Step 3: File type support check
      const step3Start = Date.now()
      const isSupported = await this.fileProcessor.isFileTypeSupported(fileType)
      steps.push({
        step: 'file_type_support_check',
        timestamp: new Date().toISOString(),
        success: isSupported,
        data: { fileType, isSupported },
        error: isSupported ? undefined : `File type '${fileType}' is not supported for content processing`,
        duration: Date.now() - step3Start
      })

      if (!isSupported) {
        return this.createResult(steps, null, `File type '${fileType}' is not supported`, startTime, filePath)
      }

      // Step 4: File read attempt
      const step4Start = Date.now()
      let fileBuffer: Buffer | null = null
      try {
        fileBuffer = fs.readFileSync(filePath)
        steps.push({
          step: 'file_read',
          timestamp: new Date().toISOString(),
          success: true,
          data: { bufferSize: fileBuffer.length },
          duration: Date.now() - step4Start
        })
      } catch (error) {
        steps.push({
          step: 'file_read',
          timestamp: new Date().toISOString(),
          success: false,
          error: `Failed to read file: ${error.message}`,
          duration: Date.now() - step4Start
        })
        return this.createResult(steps, null, `Failed to read file: ${error.message}`, startTime, filePath)
      }

      // Step 5: Content processing
      const step5Start = Date.now()
      try {
        const processedContent = await this.fileProcessor.processFile(filePath, fileType)
        
        if (processedContent.error) {
          steps.push({
            step: 'content_processing',
            timestamp: new Date().toISOString(),
            success: false,
            error: processedContent.error,
            data: { metadata: processedContent.metadata },
            duration: Date.now() - step5Start
          })
          return this.createResult(steps, null, processedContent.error, startTime, filePath)
        }

        if (!processedContent.text) {
          steps.push({
            step: 'content_processing',
            timestamp: new Date().toISOString(),
            success: false,
            error: 'No text content extracted',
            data: { metadata: processedContent.metadata },
            duration: Date.now() - step5Start
          })
          return this.createResult(steps, null, 'No text content extracted', startTime, filePath)
        }

        steps.push({
          step: 'content_processing',
          timestamp: new Date().toISOString(),
          success: true,
          data: {
            textLength: processedContent.text.length,
            metadata: processedContent.metadata
          },
          duration: Date.now() - step5Start
        })

        finalContent = processedContent.text

      } catch (error) {
        steps.push({
          step: 'content_processing',
          timestamp: new Date().toISOString(),
          success: false,
          error: `Processing error: ${error.message}`,
          duration: Date.now() - step5Start
        })
        return this.createResult(steps, null, `Processing error: ${error.message}`, startTime, filePath)
      }

      return this.createResult(steps, finalContent, null, startTime, filePath)

    } catch (error) {
      steps.push({
        step: 'unexpected_error',
        timestamp: new Date().toISOString(),
        success: false,
        error: `Unexpected error: ${error.message}`,
        duration: 0
      })
      return this.createResult(steps, null, `Unexpected error: ${error.message}`, startTime, filePath)
    }
  }

  private createResult(
    steps: ParsingStep[],
    content: string | null,
    error: string | null,
    startTime: number,
    filePath: string
  ): ParsingResult {
    const stats = fs.existsSync(filePath) ? fs.statSync(filePath) : null
    
    return {
      success: error === null && content !== null,
      steps,
      finalContent: content,
      finalError: error,
      totalDuration: Date.now() - startTime,
      fileInfo: {
        path: filePath,
        filename: path.basename(filePath),
        size: stats?.size || 0,
        type: this.getFileType(path.basename(filePath)),
        exists: fs.existsSync(filePath)
      }
    }
  }

  private getFileType(filename: string): string {
    const ext = path.extname(filename).toLowerCase()
    const typeMap: { [key: string]: string } = {
      '.pdf': 'pdf',
      '.doc': 'word',
      '.docx': 'word',
      '.txt': 'text',
      '.md': 'markdown',
      '.jpg': 'image',
      '.png': 'image'
    }
    return typeMap[ext] || 'unknown'
  }

  // Utility method to format the debugging output
  static formatResult(result: ParsingResult): string {
    let output = `=== File Parsing Debug Report ===\n`
    output += `File: ${result.fileInfo.filename} (${result.fileInfo.type})\n`
    output += `Path: ${result.fileInfo.path}\n`
    output += `Size: ${result.fileInfo.size} bytes\n`
    output += `Exists: ${result.fileInfo.exists}\n`
    output += `Total Duration: ${result.totalDuration}ms\n`
    output += `Success: ${result.success}\n\n`

    if (result.finalError) {
      output += `Final Error: ${result.finalError}\n\n`
    }

    if (result.finalContent) {
      output += `Content Length: ${result.finalContent.length} characters\n`
      output += `Content Preview: ${result.finalContent.substring(0, 200)}...\n\n`
    }

    output += `=== Detailed Steps ===\n`
    result.steps.forEach((step, index) => {
      output += `${index + 1}. ${step.step} (${step.duration || 0}ms)\n`
      output += `   Status: ${step.success ? '✅ PASS' : '❌ FAIL'}\n`
      if (step.data) {
        output += `   Data: ${JSON.stringify(step.data, null, 2)}\n`
      }
      if (step.error) {
        output += `   Error: ${step.error}\n`
      }
      output += '\n'
    })

    return output
  }
}