# FileRoutingService Implementation

## Overview

The FileRoutingService is a unified file handling system that replaces the complex SharedDropboxService with a clean, extensible architecture. This implementation follows the design specification in `FILE_HANDLING_STRATEGY.md`.

## Architecture

```
┌─────────────────────┐    ┌─────────────────────┐    ┌─────────────────────┐
│   UnifiedDropZone   │───▶│  FileRoutingService │───▶│   Electron IPC      │
│   Component         │    │                     │    │   (vault API)       │
└─────────────────────┘    └─────────────────────┘    └─────────────────────┘
           │                           │                           │
           ▼                           ▼                           ▼
┌─────────────────────┐    ┌─────────────────────┐    ┌─────────────────────┐
│  useFileOperations  │    │  File Validation    │    │   File System       │
│  Hook               │    │  & Routing Logic    │    │                     │
└─────────────────────┘    └─────────────────────┘    └─────────────────────┘
```

## Key Components

### 1. FileRoutingService (`src/services/fileRoutingService.ts`)

**Core Features:**
- Unified file upload handling for all drop zones
- Automatic file type routing (images → `/images`, documents → `/documents`)
- Context-aware file placement
- Built-in validation and error handling
- Performance metrics tracking

**Key Methods:**
- `handleFileDrop(files, destination)` - Main entry point for file uploads
- `uploadToContext(files, contextId)` - Context-specific uploads
- `uploadToPath(files, path)` - Path-specific uploads
- `readFile(request)` - Read file content
- `listFiles(request)` - List directory contents
- `deleteFiles(request)` - Delete files

### 2. UnifiedDropZone (`src/components/UnifiedDropZone.tsx`)

**Features:**
- Consistent drag & drop behavior across all UI components
- Visual feedback for drag states
- Progress indicators
- File validation
- Customizable styling and behavior

**Specialized Components:**
- `ContextDropZone` - For context-specific uploads
- `PathDropZone` - For path-specific uploads
- `ImageDropZone` - For image-only uploads
- `DocumentDropZone` - For document uploads

### 3. useFileOperations Hook (`src/hooks/useFileOperations.ts`)

**Features:**
- React state management for file operations
- Progress tracking
- Error handling
- Validation utilities

**Additional Hooks:**
- `useContextFileOperations(contextId)` - Context-specific operations
- `useFileValidation()` - File validation utilities

## Usage Examples

### Basic Context Upload

```typescript
import { ContextDropZone } from '../components/UnifiedDropZone'

const MyComponent = () => {
  return (
    <ContextDropZone
      contextId="my-context-id"
      onUploadComplete={(files) => {
        console.log('Uploaded:', files)
      }}
      onUploadError={(error) => {
        console.error('Error:', error)
      }}
    />
  )
}
```

### Advanced Upload with Validation

```typescript
import { UnifiedDropZone } from '../components/UnifiedDropZone'

const AdvancedUpload = () => {
  return (
    <UnifiedDropZone
      contextId="my-context"
      acceptedTypes={['image/*', 'application/pdf']}
      maxFiles={5}
      showProgress={true}
      showFileList={true}
      onUploadComplete={(files) => {
        // Handle successful upload
      }}
      onUploadError={(error) => {
        // Handle error
      }}
    />
  )
}
```

### Using the Hook Directly

```typescript
import { useFileOperations } from '../hooks/useFileOperations'

const MyComponent = () => {
  const { uploadFiles, state } = useFileOperations()
  
  const handleUpload = async (files: File[]) => {
    try {
      const result = await uploadFiles(files, {
        contextId: 'my-context',
        autoRoute: true
      })
      console.log('Uploaded:', result)
    } catch (error) {
      console.error('Upload failed:', error)
    }
  }
  
  return (
    <div>
      {state.uploading && <p>Uploading...</p>}
      {/* Your UI */}
    </div>
  )
}
```

## Migration from SharedDropboxService

### Before (SharedDropboxService)

```typescript
// Old way - complex and inconsistent
import { sharedDropboxService } from '../services/sharedDropboxService'

const handleFileDrop = async (e: React.DragEvent, contextId?: string) => {
  e.preventDefault()
  const files = Array.from(e.dataTransfer.files)
  
  try {
    await sharedDropboxService.addFilesToContext(files, contextId)
    // Manual refresh needed
    await loadVaultData()
  } catch (error) {
    console.error('Error:', error)
  }
}
```

### After (FileRoutingService)

```typescript
// New way - clean and consistent
import { ContextDropZone } from '../components/UnifiedDropZone'

const MyComponent = () => {
  return (
    <ContextDropZone
      contextId={contextId}
      onUploadComplete={(files) => {
        console.log('Success:', files)
        // Auto-refresh handled by the system
      }}
      onUploadError={(error) => {
        console.error('Error:', error)
      }}
    />
  )
}
```

## Configuration

### File Routing Rules

The service uses configurable routing rules:

```typescript
const config = {
  autoRouting: {
    enabled: true,
    rules: [
      { pattern: 'image/*', destination: 'images' },
      { pattern: 'text/*', destination: 'documents' },
      { pattern: 'application/pdf', destination: 'documents' },
      { pattern: 'application/json', destination: 'code' }
    ]
  }
}
```

### Validation Settings

```typescript
const config = {
  maxFileSize: 100 * 1024 * 1024, // 100MB
  allowedTypes: ['image/*', 'text/*', 'application/pdf', 'application/json'],
  security: {
    scanFiles: false,
    quarantineUnsafe: false
  }
}
```

## File Structure

```
src/
├── services/
│   └── fileRoutingService.ts      # Main service implementation
├── components/
│   └── UnifiedDropZone.tsx        # Drop zone component
├── hooks/
│   └── useFileOperations.ts       # React hooks
├── types/
│   └── fileRouting.ts             # TypeScript interfaces
└── examples/
    └── HomePageIntegration.tsx    # Integration examples
```

## Benefits Over SharedDropboxService

### 1. **Simplified Architecture**
- Single service handles all file operations
- Consistent API across all components
- Reduced complexity and maintenance burden

### 2. **Better User Experience**
- Unified drop zone behavior
- Real-time progress indicators
- Better error handling and feedback

### 3. **Developer Experience**
- Clean, typed interfaces
- React hooks for easy integration
- Comprehensive examples and documentation

### 4. **Extensibility**
- Plugin-ready architecture
- Configurable routing rules
- Easy to add new file types and processors

### 5. **Performance**
- Built-in metrics tracking
- Optimized for local file operations
- Reduced IPC overhead

## Next Steps (Future Phases)

### Phase 2: Plugin System
- Implement `FileProcessorPlugin` interface
- Add metadata extraction plugins
- Thumbnail generation plugins
- Text extraction plugins

### Phase 3: Advanced Features
- Batch operations
- File versioning
- Advanced security scanning
- Cloud storage integration

### Phase 4: Developer Platform
- Token-based API access
- Third-party plugin support
- Webhook system
- API rate limiting

## Testing

The implementation includes comprehensive validation and error handling. Key test scenarios:

1. **File Upload Tests**
   - Valid file uploads
   - Invalid file type rejection
   - File size limit enforcement
   - Context routing validation

2. **Drop Zone Tests**
   - Drag and drop behavior
   - Visual feedback states
   - Progress indication
   - Error display

3. **Hook Tests**
   - State management
   - Error handling
   - Progress tracking
   - Cleanup on unmount

## Troubleshooting

### Common Issues

1. **Files not uploading**
   - Check file size limits
   - Verify file type is allowed
   - Ensure context exists

2. **Drop zone not responding**
   - Verify electronAPI is available
   - Check component props
   - Ensure service is initialized

3. **Progress not updating**
   - Check hook integration
   - Verify progress callbacks
   - Ensure state updates

### Debug Mode

Enable debug logging:

```typescript
const fileRoutingService = new FileRoutingService({
  logLevel: 'debug',
  enableConsoleLogging: true
})
```

This implementation provides a solid foundation for ChatLo's file handling while maintaining the flexibility needed for future enhancements and plugin development.
