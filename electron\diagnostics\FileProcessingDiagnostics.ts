// TODO: MARK FOR REMOVAL - This diagnostic module is not necessary for core functionality
// The compiled version was causing syntax errors and has been removed from dist/
// Consider removing this entire file and its references in a future cleanup
import * as fs from 'fs'
import * as path from 'path'
import { FileSystemManager } from '../fileSystem'
import { FileProcessorService } from '../fileProcessors'



export interface DiagnosticResult {
  step: string
  status: 'success' | 'warning' | 'error'
  message: string
  details?: any
  timestamp: string
}

export interface FileProcessingDiagnostic {
  filePath: string
  filename: string
  fileSize: number
  fileType: string
  mimeType: string
  results: DiagnosticResult[]
  summary: {
    totalSteps: number
    successCount: number
    warningCount: number
    errorCount: number
    overallStatus: 'success' | 'warning' | 'error'
  }
}

export class FileProcessingDiagnostics {
  private fileSystemManager: FileSystemManager
  private fileProcessor: FileProcessorService

  constructor(fileSystemManager: FileSystemManager, fileProcessor: FileProcessorService) {
    this.fileSystemManager = fileSystemManager
    this.fileProcessor = fileProcessor
  }

  /**
   * Run comprehensive diagnostics on a file processing workflow
   */
  async diagnoseFileProcessing(filePath: string): Promise<FileProcessingDiagnostic> {
    const results: DiagnosticResult[] = []
    const filename = path.basename(filePath)

    // Ensure file processor is initialized
    try {
      if (typeof (this.fileProcessor as any).initialize === 'function') {
        await (this.fileProcessor as any).initialize()
      }
    } catch (error: any) {
      results.push({
        step: 'File Processor Initialization',
        status: 'warning',
        message: `File processor initialization warning: ${error.message}`,
        details: { error: error.stack },
        timestamp: new Date().toISOString()
      })
    }
    
    // Initialize diagnostic report
    const diagnostic: FileProcessingDiagnostic = {
      filePath,
      filename,
      fileSize: 0,
      fileType: '',
      mimeType: '',
      results,
      summary: {
        totalSteps: 0,
        successCount: 0,
        warningCount: 0,
        errorCount: 0,
        overallStatus: 'success'
      }
    }

    try {
      // Step 1: File System Validation
      await this.validateFileSystem(filePath, results)
      
      // Step 2: File Properties Analysis
      const fileInfo = await this.analyzeFileProperties(filePath, results)
      diagnostic.fileSize = fileInfo.size
      diagnostic.fileType = fileInfo.type
      diagnostic.mimeType = fileInfo.mimeType
      
      // Step 3: File Type Support Check
      await this.checkFileTypeSupport(fileInfo.type, results)
      
      // Step 4: Plugin System Diagnostics
      await this.diagnosePluginSystem(filePath, fileInfo.type, results)
      
      // Step 5: Content Processing Test
      await this.testContentProcessing(filePath, fileInfo.type, results)
      
      // Step 6: Database Integration Test
      await this.testDatabaseIntegration(filePath, results)

    } catch (error: any) {
      results.push({
        step: 'Critical Error',
        status: 'error',
        message: `Diagnostic process failed: ${error.message}`,
        details: { error: error.stack },
        timestamp: new Date().toISOString()
      })
    }

    // Calculate summary
    this.calculateSummary(diagnostic)
    
    return diagnostic
  }

  private async validateFileSystem(filePath: string, results: DiagnosticResult[]): Promise<void> {
    try {
      // Check if file exists
      if (!fs.existsSync(filePath)) {
        results.push({
          step: 'File Existence Check',
          status: 'error',
          message: 'File does not exist at the specified path',
          details: { filePath },
          timestamp: new Date().toISOString()
        })
        return
      }

      // Check file permissions
      try {
        fs.accessSync(filePath, fs.constants.R_OK)
        results.push({
          step: 'File Permissions',
          status: 'success',
          message: 'File is readable',
          timestamp: new Date().toISOString()
        })
      } catch (error) {
        results.push({
          step: 'File Permissions',
          status: 'error',
          message: 'File is not readable',
          details: { error: (error as Error).message },
          timestamp: new Date().toISOString()
        })
      }

      // Check if it's actually a file (not directory)
      const stats = fs.statSync(filePath)
      if (!stats.isFile()) {
        results.push({
          step: 'File Type Validation',
          status: 'error',
          message: 'Path does not point to a regular file',
          details: { isDirectory: stats.isDirectory(), isFile: stats.isFile() },
          timestamp: new Date().toISOString()
        })
      } else {
        results.push({
          step: 'File Type Validation',
          status: 'success',
          message: 'Path points to a valid file',
          timestamp: new Date().toISOString()
        })
      }

    } catch (error: any) {
      results.push({
        step: 'File System Validation',
        status: 'error',
        message: `File system validation failed: ${error.message}`,
        details: { error: error.stack },
        timestamp: new Date().toISOString()
      })
    }
  }

  private async analyzeFileProperties(filePath: string, results: DiagnosticResult[]): Promise<{
    size: number, type: string, mimeType: string
  }> {
    try {
      const stats = fs.statSync(filePath)
      const filename = path.basename(filePath)
      const extension = path.extname(filename).toLowerCase()
      
      // Get file type using FileSystemManager's logic
      const fileType = this.fileSystemManager.getFileType(filename)
      const mimeType = this.fileSystemManager.getMimeType(filename)

      results.push({
        step: 'File Properties Analysis',
        status: 'success',
        message: 'File properties analyzed successfully',
        details: {
          filename,
          extension,
          size: stats.size,
          sizeFormatted: this.formatFileSize(stats.size),
          fileType,
          mimeType,
          lastModified: stats.mtime.toISOString(),
          created: stats.birthtime.toISOString()
        },
        timestamp: new Date().toISOString()
      })

      return { size: stats.size, type: fileType, mimeType }

    } catch (error: any) {
      results.push({
        step: 'File Properties Analysis',
        status: 'error',
        message: `Failed to analyze file properties: ${error.message}`,
        details: { error: error.stack },
        timestamp: new Date().toISOString()
      })
      return { size: 0, type: 'unknown', mimeType: 'application/octet-stream' }
    }
  }

  private async checkFileTypeSupport(fileType: string, results: DiagnosticResult[]): Promise<void> {
    try {
      console.log('Checking file type support for:', fileType)
      console.log('FileProcessor initialized:', (this.fileProcessor as any).initialized)
      const isSupported = await this.fileProcessor.isFileTypeSupported(fileType)
      console.log('File type support result:', isSupported)
      
      if (isSupported) {
        results.push({
          step: 'File Type Support Check',
          status: 'success',
          message: `File type '${fileType}' is supported for processing`,
          details: { fileType, supported: true },
          timestamp: new Date().toISOString()
        })
      } else {
        results.push({
          step: 'File Type Support Check',
          status: 'warning',
          message: `File type '${fileType}' is not supported for content processing`,
          details: { fileType, supported: false },
          timestamp: new Date().toISOString()
        })
      }

    } catch (error: any) {
      results.push({
        step: 'File Type Support Check',
        status: 'error',
        message: `Failed to check file type support: ${error.message}`,
        details: { error: error.stack },
        timestamp: new Date().toISOString()
      })
    }
  }

  private async diagnosePluginSystem(filePath: string, fileType: string, results: DiagnosticResult[]): Promise<void> {
    try {
      // Check if using plugin system (PluginFileProcessorService)
      const isPluginProcessor = this.fileProcessor.constructor.name === 'PluginFileProcessorService'

      results.push({
        step: 'Plugin System Status',
        status: 'success',
        message: `Using ${isPluginProcessor ? 'plugin' : 'legacy'} processing system`,
        details: {
          usingPluginSystem: isPluginProcessor,
          processorType: this.fileProcessor.constructor.name
        },
        timestamp: new Date().toISOString()
      })

      if (isPluginProcessor) {
        // Access the plugin manager through the file processor
        const pluginManager = (this.fileProcessor as any).pluginManager

        if (pluginManager && typeof pluginManager.getAllPlugins === 'function') {
          // Get all available plugins
          const allPlugins = pluginManager.getAllPlugins()

          // Filter plugins that can handle this file type
          const compatiblePlugins = allPlugins.filter((plugin: any) => {
            return plugin.supportedTypes && plugin.supportedTypes.includes(fileType)
          })

          results.push({
            step: 'Plugin System Diagnostics',
            status: compatiblePlugins.length > 0 ? 'success' : 'warning',
            message: `Found ${compatiblePlugins.length} plugin(s) for file type '${fileType}' (${allPlugins.length} total plugins)`,
            details: {
              totalPlugins: allPlugins.length,
              compatiblePlugins: compatiblePlugins.length,
              plugins: compatiblePlugins.map((p: any) => ({
                name: p.name,
                version: p.version,
                supportedTypes: p.supportedTypes
              })),
              allPlugins: allPlugins.map((p: any) => ({
                name: p.name,
                version: p.version,
                supportedTypes: p.supportedTypes
              }))
            },
            timestamp: new Date().toISOString()
          })

          // Test plugin initialization for PDF files specifically
          if (fileType === 'pdf') {
            await this.testPDFPluginInitialization(results)
          }
        } else {
          results.push({
            step: 'Plugin System Diagnostics',
            status: 'error',
            message: 'Plugin manager not accessible or getAllPlugins method not found',
            details: {
              pluginManagerExists: !!pluginManager,
              getAllPluginsExists: pluginManager && typeof pluginManager.getAllPlugins === 'function'
            },
            timestamp: new Date().toISOString()
          })
        }
      }

    } catch (error: any) {
      results.push({
        step: 'Plugin System Diagnostics',
        status: 'error',
        message: `Plugin system diagnostics failed: ${error.message}`,
        details: { error: error.stack },
        timestamp: new Date().toISOString()
      })
    }
  }

  private async testPDFPluginInitialization(results: DiagnosticResult[]): Promise<void> {
    try {
      // Test pdf-parse module availability
      try {
        const pdfParse = await import('pdf-parse') as any
        results.push({
          step: 'PDF Module Check',
          status: 'success',
          message: 'pdf-parse module is available and can be imported',
          details: { moduleAvailable: true, hasDefault: !!pdfParse.default },
          timestamp: new Date().toISOString()
        })
      } catch (error: any) {
        results.push({
          step: 'PDF Module Check',
          status: 'error',
          message: 'pdf-parse module is not available or cannot be imported',
          details: { error: error.message },
          timestamp: new Date().toISOString()
        })
      }

    } catch (error: any) {
      results.push({
        step: 'PDF Plugin Initialization Test',
        status: 'error',
        message: `PDF plugin test failed: ${error.message}`,
        details: { error: error.stack },
        timestamp: new Date().toISOString()
      })
    }
  }

  private async testContentProcessing(filePath: string, fileType: string, results: DiagnosticResult[]): Promise<void> {
    try {
      const startTime = Date.now()
      
      results.push({
        step: 'Content Processing Start',
        status: 'success',
        message: 'Starting content processing test',
        details: { filePath, fileType },
        timestamp: new Date().toISOString()
      })

      const processedContent = await this.fileProcessor.processFile(filePath, fileType)
      const processingTime = Date.now() - startTime

      if (processedContent.error) {
        results.push({
          step: 'Content Processing',
          status: 'error',
          message: `Content processing failed: ${processedContent.error}`,
          details: { 
            error: processedContent.error,
            processingTime,
            hasText: !!processedContent.text,
            textLength: processedContent.text?.length || 0
          },
          timestamp: new Date().toISOString()
        })
      } else if (processedContent.text) {
        results.push({
          step: 'Content Processing',
          status: 'success',
          message: `Content processed successfully in ${processingTime}ms`,
          details: {
            processingTime,
            textLength: processedContent.text.length,
            hasMetadata: !!processedContent.metadata,
            metadata: processedContent.metadata
          },
          timestamp: new Date().toISOString()
        })
      } else {
        results.push({
          step: 'Content Processing',
          status: 'warning',
          message: 'Processing completed but no text content was extracted',
          details: { processingTime, hasMetadata: !!processedContent.metadata },
          timestamp: new Date().toISOString()
        })
      }

    } catch (error: any) {
      results.push({
        step: 'Content Processing Test',
        status: 'error',
        message: `Content processing test failed: ${error.message}`,
        details: { error: error.stack },
        timestamp: new Date().toISOString()
      })
    }
  }

  private async testDatabaseIntegration(filePath: string, results: DiagnosticResult[]): Promise<void> {
    try {
      // Test file indexing without content processing
      const indexResult = await this.fileSystemManager.indexFile(filePath, false)
      
      if (indexResult) {
        results.push({
          step: 'Database Integration',
          status: 'success',
          message: 'File successfully indexed in database',
          details: { fileId: indexResult },
          timestamp: new Date().toISOString()
        })
      } else {
        results.push({
          step: 'Database Integration',
          status: 'error',
          message: 'Failed to index file in database',
          timestamp: new Date().toISOString()
        })
      }

    } catch (error: any) {
      results.push({
        step: 'Database Integration Test',
        status: 'error',
        message: `Database integration test failed: ${error.message}`,
        details: { error: error.stack },
        timestamp: new Date().toISOString()
      })
    }
  }

  private calculateSummary(diagnostic: FileProcessingDiagnostic): void {
    const { results } = diagnostic
    
    diagnostic.summary.totalSteps = results.length
    diagnostic.summary.successCount = results.filter(r => r.status === 'success').length
    diagnostic.summary.warningCount = results.filter(r => r.status === 'warning').length
    diagnostic.summary.errorCount = results.filter(r => r.status === 'error').length
    
    // Determine overall status
    if (diagnostic.summary.errorCount > 0) {
      diagnostic.summary.overallStatus = 'error'
    } else if (diagnostic.summary.warningCount > 0) {
      diagnostic.summary.overallStatus = 'warning'
    } else {
      diagnostic.summary.overallStatus = 'success'
    }
  }

  private formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }
}
