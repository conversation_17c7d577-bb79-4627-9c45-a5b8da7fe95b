/**
 * UnifiedDropZone Component
 * Standardized drop zone component for consistent file handling across the app
 */

import React, { useState, useCallback, useRef } from 'react'
import { useFileOperations, useFileValidation } from '../hooks/useFileOperations'
import { FileDestination, UploadedFile } from '../types/fileRouting'

export interface UnifiedDropZoneProps {
  // Destination configuration
  contextId?: string
  path?: string
  autoRoute?: boolean
  
  // Styling and behavior
  className?: string
  children?: React.ReactNode
  disabled?: boolean
  multiple?: boolean
  
  // Event handlers
  onUploadComplete?: (files: UploadedFile[]) => void
  onUploadError?: (error: string) => void
  onUploadProgress?: (progress: number) => void
  onFilesSelected?: (files: File[]) => void
  
  // Visual customization
  dragActiveClassName?: string
  dragOverClassName?: string
  showProgress?: boolean
  showFileList?: boolean
  
  // Validation
  maxFiles?: number
  acceptedTypes?: string[]
}

export const UnifiedDropZone: React.FC<UnifiedDropZoneProps> = ({
  contextId,
  path,
  autoRoute = true,
  className = '',
  children,
  disabled = false,
  multiple = true,
  onUploadComplete,
  onUploadError,
  onUploadProgress,
  onFilesSelected,
  dragActiveClassName = 'drag-active',
  dragOverClassName = 'drag-over',
  showProgress = true,
  showFileList = false,
  maxFiles,
  acceptedTypes
}) => {
  const [isDragActive, setIsDragActive] = useState(false)
  const [isDragOver, setIsDragOver] = useState(false)
  const [selectedFiles, setSelectedFiles] = useState<File[]>([])
  
  const fileInputRef = useRef<HTMLInputElement>(null)
  const dragCounterRef = useRef(0)
  
  const { uploadFiles, state } = useFileOperations()
  const { validateFiles } = useFileValidation()

  /**
   * Handle file drop
   */
  const handleDrop = useCallback(async (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    
    setIsDragActive(false)
    setIsDragOver(false)
    dragCounterRef.current = 0
    
    if (disabled) return
    
    const files = Array.from(e.dataTransfer.files)
    await processFiles(files)
  }, [disabled])

  /**
   * Handle file input change
   */
  const handleFileInputChange = useCallback(async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || [])
    await processFiles(files)
    
    // Reset input
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }, [])

  /**
   * Process selected files
   */
  const processFiles = useCallback(async (files: File[]) => {
    if (files.length === 0) return
    
    // Apply file limits
    let filesToProcess = files
    if (maxFiles && files.length > maxFiles) {
      filesToProcess = files.slice(0, maxFiles)
      onUploadError?.(`Only ${maxFiles} files allowed. Processing first ${maxFiles} files.`)
    }
    
    if (!multiple && filesToProcess.length > 1) {
      filesToProcess = [filesToProcess[0]]
      onUploadError?.('Only one file allowed. Processing first file.')
    }
    
    // Filter by accepted types if specified
    if (acceptedTypes && acceptedTypes.length > 0) {
      filesToProcess = filesToProcess.filter(file => {
        return acceptedTypes.some(type => {
          if (type.endsWith('/*')) {
            return file.type.startsWith(type.slice(0, -2))
          }
          return file.type === type
        })
      })
    }
    
    // Validate files
    const validation = validateFiles(filesToProcess)
    if (!validation.valid) {
      const errorMessage = validation.errors.map(err => `${err.filename}: ${err.message}`).join('\n')
      onUploadError?.(errorMessage)
      return
    }
    
    setSelectedFiles(filesToProcess)
    onFilesSelected?.(filesToProcess)
    
    try {
      const destination: FileDestination = {
        contextId,
        path,
        autoRoute
      }
      
      const uploadedFiles = await uploadFiles(filesToProcess, destination, onUploadProgress)
      onUploadComplete?.(uploadedFiles)
      setSelectedFiles([])
    } catch (error: any) {
      onUploadError?.(error.message || 'Upload failed')
    }
  }, [
    maxFiles,
    multiple,
    acceptedTypes,
    validateFiles,
    contextId,
    path,
    autoRoute,
    uploadFiles,
    onUploadComplete,
    onUploadError,
    onUploadProgress,
    onFilesSelected
  ])

  /**
   * Drag event handlers
   */
  const handleDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    
    dragCounterRef.current++
    
    if (e.dataTransfer.items && e.dataTransfer.items.length > 0) {
      setIsDragActive(true)
    }
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    
    dragCounterRef.current--
    
    if (dragCounterRef.current === 0) {
      setIsDragActive(false)
      setIsDragOver(false)
    }
  }, [])

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    
    if (!isDragOver) {
      setIsDragOver(true)
    }
  }, [isDragOver])

  /**
   * Click to select files
   */
  const handleClick = useCallback(() => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click()
    }
  }, [disabled])

  /**
   * Build CSS classes
   */
  const getClassName = useCallback(() => {
    let classes = `unified-drop-zone ${className}`
    
    if (isDragActive) {
      classes += ` ${dragActiveClassName}`
    }
    
    if (isDragOver) {
      classes += ` ${dragOverClassName}`
    }
    
    if (disabled) {
      classes += ' disabled'
    }
    
    if (state.uploading) {
      classes += ' uploading'
    }
    
    return classes.trim()
  }, [className, isDragActive, isDragOver, disabled, state.uploading, dragActiveClassName, dragOverClassName])

  /**
   * Render progress indicator
   */
  const renderProgress = () => {
    if (!showProgress || !state.uploading) return null
    
    const progressValues = Object.values(state.uploadProgress)
    const totalProgress = progressValues.length > 0 
      ? progressValues.reduce((sum, p) => sum + p, 0) / progressValues.length 
      : 0
    
    return (
      <div className="upload-progress">
        <div className="progress-bar">
          <div 
            className="progress-fill" 
            style={{ width: `${totalProgress}%` }}
          />
        </div>
        <span className="progress-text">{Math.round(totalProgress)}%</span>
      </div>
    )
  }

  /**
   * Render file list
   */
  const renderFileList = () => {
    if (!showFileList || selectedFiles.length === 0) return null
    
    return (
      <div className="selected-files">
        <h4>Selected Files:</h4>
        <ul>
          {selectedFiles.map((file, index) => (
            <li key={index} className="file-item">
              <span className="file-name">{file.name}</span>
              <span className="file-size">({(file.size / 1024).toFixed(1)} KB)</span>
            </li>
          ))}
        </ul>
      </div>
    )
  }

  return (
    <div
      className={getClassName()}
      onDrop={handleDrop}
      onDragEnter={handleDragEnter}
      onDragLeave={handleDragLeave}
      onDragOver={handleDragOver}
      onClick={handleClick}
      role="button"
      tabIndex={disabled ? -1 : 0}
      aria-disabled={disabled}
      aria-label="Drop files here or click to select"
    >
      <input
        ref={fileInputRef}
        type="file"
        multiple={multiple}
        accept={acceptedTypes?.join(',')}
        onChange={handleFileInputChange}
        style={{ display: 'none' }}
        disabled={disabled}
      />
      
      {children || (
        <div className="drop-zone-content">
          {state.uploading ? (
            <div className="uploading-state">
              <div className="spinner" />
              <p>Uploading files...</p>
            </div>
          ) : (
            <div className="default-state">
              <div className="drop-icon">📁</div>
              <p className="drop-text">
                {isDragActive 
                  ? 'Drop files here' 
                  : 'Drop files here or click to select'
                }
              </p>
              {acceptedTypes && (
                <p className="accepted-types">
                  Accepted: {acceptedTypes.join(', ')}
                </p>
              )}
            </div>
          )}
        </div>
      )}
      
      {renderProgress()}
      {renderFileList()}
      
      {state.error && (
        <div className="error-message">
          {state.error}
        </div>
      )}
    </div>
  )
}

/**
 * Specialized drop zone for context uploads
 */
export const ContextDropZone: React.FC<Omit<UnifiedDropZoneProps, 'contextId'> & { contextId: string }> = (props) => {
  return <UnifiedDropZone {...props} autoRoute={true} />
}

/**
 * Specialized drop zone for path uploads
 */
export const PathDropZone: React.FC<Omit<UnifiedDropZoneProps, 'path'> & { path: string }> = (props) => {
  return <UnifiedDropZone {...props} autoRoute={false} />
}

/**
 * Specialized drop zone for image uploads
 */
export const ImageDropZone: React.FC<UnifiedDropZoneProps> = (props) => {
  return (
    <UnifiedDropZone 
      {...props} 
      acceptedTypes={['image/*']}
      maxFiles={props.maxFiles || 10}
    />
  )
}

/**
 * Specialized drop zone for document uploads
 */
export const DocumentDropZone: React.FC<UnifiedDropZoneProps> = (props) => {
  return (
    <UnifiedDropZone 
      {...props} 
      acceptedTypes={['text/*', 'application/pdf', 'application/json']}
    />
  )
}
