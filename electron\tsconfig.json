{"compilerOptions": {"target": "ES2020", "module": "CommonJS", "lib": ["ES2020"], "outDir": "../dist", "rootDir": ".", "strict": false, "noImplicitAny": false, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "declaration": false, "sourceMap": false, "typeRoots": ["./types", "../node_modules/@types"]}, "include": ["**/*.ts", "types/**/*.d.ts"], "exclude": ["node_modules", "dist"]}