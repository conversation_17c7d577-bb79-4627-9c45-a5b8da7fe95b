import { OpenRouterModel, ChatCompletionRequest } from '../types'
import { BaseService, ServiceError, ServiceErrorCode } from './base'

export class OpenRouterService extends BaseService {
  private apiKey: string | null = null
  private baseUrl = 'https://openrouter.ai/api/v1'

  constructor() {
    super({
      name: 'OpenRouterService',
      autoInitialize: true
    })
  }

  /**
   * Initialize the service
   */
  protected async doInitialize(): Promise<void> {
    this.logger.info('OpenRouter service initialized', 'doInitialize', {
      baseUrl: this.baseUrl,
      hasApiKey: !!this.apiKey
    })
  }

  /**
   * Health check implementation
   */
  protected async doHealthCheck(): Promise<boolean> {
    try {
      // Simple connectivity check to OpenRouter API
      const response = await fetch(`${this.baseUrl}/models`, {
        method: 'HEAD',
        headers: this.apiKey ? { 'Authorization': `Bearer ${this.apiKey}` } : {}
      })
      return response.status < 500 // Accept 401/403 as "healthy" since it means API is responding
    } catch (error) {
      this.logger.warn('Health check failed', 'doHealthCheck', error)
      return false
    }
  }

  /**
   * Cleanup implementation
   */
  protected async doCleanup(): Promise<void> {
    this.apiKey = null
    this.logger.info('OpenRouter service cleaned up', 'doCleanup')
  }

  setApiKey(apiKey: string) {
    this.apiKey = apiKey
    this.logger.info('API key updated', 'setApiKey', { hasApiKey: !!apiKey })
  }

  async validateApiKey(): Promise<{ valid: boolean; error?: string }> {
    const result = await this.executeOperation(
      'validateApiKey',
      async () => {
        if (!this.apiKey) {
          throw new ServiceError(
            ServiceErrorCode.VALIDATION_ERROR,
            'No API key provided',
            { serviceName: this.serviceName, operation: 'validateApiKey' }
          )
        }

        const response = await fetch(`${this.baseUrl}/models`, {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
          },
        })

        if (response.ok) {
          return { valid: true }
        } else {
          const errorData = await response.json().catch(() => ({}))
          const errorMessage = `API key validation failed (${response.status}): ${errorData.error?.message || response.statusText}`

          throw new ServiceError(
            this.getErrorCodeFromStatus(response.status),
            errorMessage,
            { serviceName: this.serviceName, operation: 'validateApiKey', details: { status: response.status, errorData } }
          )
        }
      }
    )

    if (!result.success) {
      return {
        valid: false,
        error: result.error!.getUserMessage()
      }
    }

    return result.data!
  }

  async getModels(): Promise<OpenRouterModel[]> {
    const result = await this.executeOperation(
      'getModels',
      async () => {
        const response = await fetch(`${this.baseUrl}/models`, {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
          },
        })

        if (!response.ok) {
          throw new ServiceError(
            this.getErrorCodeFromStatus(response.status),
            `Failed to fetch models: ${response.statusText}`,
            { serviceName: this.serviceName, operation: 'getModels', details: { status: response.status } }
          )
        }

        const data = await response.json()
        const models = data.data || []

        // Log model statistics
        this.logger.info('Models fetched successfully', 'getModels', {
          totalModels: models.length,
          claudeModels: models.filter((m: any) => m.id.includes('claude') || m.name.toLowerCase().includes('claude')).length,
          gptModels: models.filter((m: any) => m.id.includes('gpt') || m.name.toLowerCase().includes('gpt')).length
        })

        return models
      }
    )

    if (!result.success) {
      this.logger.error('Failed to fetch models', 'getModels', result.error)
      return []
    }

    return result.data!
  }

  async createChatCompletion(request: ChatCompletionRequest): Promise<string> {
    return await this.executeOperationOrThrow(
      'createChatCompletion',
      async () => {
        if (!this.apiKey) {
          throw new ServiceError(
            ServiceErrorCode.CONFIGURATION_ERROR,
            'OpenRouter API key not set',
            { serviceName: this.serviceName, operation: 'createChatCompletion' }
          )
        }

        const response = await fetch(`${this.baseUrl}/chat/completions`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
            'HTTP-Referer': 'https://chatlo.app',
            'X-Title': 'Chatlo',
          },
          body: JSON.stringify({
            model: request.model,
            messages: request.messages,
            temperature: request.temperature || 0.7,
            max_tokens: request.max_tokens || 4096,
            top_p: request.top_p,
            top_k: request.top_k,
            frequency_penalty: request.frequency_penalty,
            presence_penalty: request.presence_penalty,
            stop: request.stop,
            stream: false,
          }),
        })

        if (!response.ok) {
          await this.handleApiError(response)
        }

        const data = await response.json()
        const content = data.choices?.[0]?.message?.content || 'No response generated'

        this.logger.info('Chat completion created successfully', 'createChatCompletion', {
          model: request.model,
          messageCount: request.messages.length,
          responseLength: content.length
        })

        return content
      },
      { model: request.model, messageCount: request.messages.length }
    )
  }

  /**
   * Handle API error responses with detailed error mapping
   */
  private async handleApiError(response: Response): Promise<never> {
    let errorData: any = {}
    try {
      errorData = await response.json()
    } catch (e) {
      this.logger.warn('Failed to parse error response', 'handleApiError', e)
    }

    this.logger.error('OpenRouter API error', 'handleApiError', {
      status: response.status,
      statusText: response.statusText,
      errorData,
      headers: Object.fromEntries(response.headers.entries())
    })

    let errorMessage = errorData.error?.message || errorData.message || 'Unknown error'

    // Add specific error messages for common status codes
    switch (response.status) {
      case 401:
        errorMessage = 'Invalid API key. Please check your OpenRouter API key in settings.'
        break
      case 403:
        errorMessage = 'Access forbidden. This might be due to insufficient credits, model restrictions, or rate limiting.'
        break
      case 429:
        errorMessage = 'Rate limit exceeded. Please wait a moment before trying again.'
        break
      case 500:
        errorMessage = 'OpenRouter server error. Please try again later.'
        break
      case 502:
      case 503:
      case 504:
        errorMessage = 'OpenRouter service temporarily unavailable. Please try again later.'
        break
    }

    throw new ServiceError(
      this.getErrorCodeFromStatus(response.status),
      `OpenRouter API error (${response.status}): ${errorMessage}`,
      { serviceName: this.serviceName, operation: 'handleApiError', details: { status: response.status, errorData } }
    )
  }

  /**
   * Map HTTP status codes to ServiceErrorCode
   */
  private getErrorCodeFromStatus(status: number): ServiceErrorCode {
    switch (status) {
      case 401:
      case 403:
        return ServiceErrorCode.AUTHENTICATION_ERROR
      case 429:
        return ServiceErrorCode.RATE_LIMIT_ERROR
      case 500:
      case 502:
      case 503:
      case 504:
        return ServiceErrorCode.EXTERNAL_SERVICE_ERROR
      case 400:
        return ServiceErrorCode.VALIDATION_ERROR
      default:
        return ServiceErrorCode.EXTERNAL_SERVICE_ERROR
    }
  }

  async createStreamingChatCompletion(
    request: ChatCompletionRequest,
    onChunk: (chunk: string) => void,
    onComplete: () => void,
    onError: (error: Error) => void
  ): Promise<void> {
    const result = await this.executeOperation(
      'createStreamingChatCompletion',
      async () => {
        if (!this.apiKey) {
          throw new ServiceError(
            ServiceErrorCode.CONFIGURATION_ERROR,
            'OpenRouter API key not set',
            { serviceName: this.serviceName, operation: 'createStreamingChatCompletion' }
          )
        }

        // Validate required parameters
        if (!request.model) {
          throw new ServiceError(
            ServiceErrorCode.VALIDATION_ERROR,
            'No model selected',
            { serviceName: this.serviceName, operation: 'createStreamingChatCompletion' }
          )
        }

        if (!request.messages || request.messages.length === 0) {
          throw new ServiceError(
            ServiceErrorCode.VALIDATION_ERROR,
            'No messages provided',
            { serviceName: this.serviceName, operation: 'createStreamingChatCompletion' }
          )
        }

        this.logger.info('Starting streaming chat completion', 'createStreamingChatCompletion', {
          model: request.model,
          messageCount: request.messages.length,
          temperature: request.temperature,
          maxTokens: request.max_tokens
        })

        await this.performStreamingRequest(request, onChunk, onComplete)
      },
      { model: request.model, messageCount: request.messages.length }
    )

    if (!result.success) {
      this.logger.error('Streaming chat completion failed', 'createStreamingChatCompletion', result.error)
      onError(new Error(result.error!.getUserMessage()))
    }
  }

  /**
   * Perform the actual streaming request
   */
  private async performStreamingRequest(
    request: ChatCompletionRequest,
    onChunk: (chunk: string) => void,
    onComplete: () => void
  ): Promise<void> {
    const response = await fetch(`${this.baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'https://chatlo.app',
        'X-Title': 'Chatlo',
      },
      body: JSON.stringify({
        model: request.model,
        messages: request.messages,
        temperature: request.temperature || 0.7,
        max_tokens: request.max_tokens || 4096,
        top_p: request.top_p,
        top_k: request.top_k,
        frequency_penalty: request.frequency_penalty,
        presence_penalty: request.presence_penalty,
        stop: request.stop,
        stream: true,
      }),
    })

    if (!response.ok) {
      await this.handleApiError(response)
    }

    const reader = response.body?.getReader()
    if (!reader) {
      throw new ServiceError(
        ServiceErrorCode.EXTERNAL_SERVICE_ERROR,
        'Failed to get response reader',
        { serviceName: this.serviceName, operation: 'performStreamingRequest' }
      )
    }

    const decoder = new TextDecoder()
    let buffer = ''
    let chunkCount = 0

    try {
      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        buffer += decoder.decode(value, { stream: true })
        const lines = buffer.split('\n')
        buffer = lines.pop() || ''

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6)
            if (data === '[DONE]') {
              this.logger.info('Streaming completed successfully', 'performStreamingRequest', { chunkCount })
              onComplete()
              return
            }

            try {
              const parsed = JSON.parse(data)
              const content = parsed.choices?.[0]?.delta?.content
              if (content) {
                onChunk(content)
                chunkCount++
              }
            } catch (e) {
              // Ignore parsing errors for individual chunks
              this.logger.debug('Failed to parse streaming chunk', 'performStreamingRequest', e)
            }
          }
        }
      }

      this.logger.info('Streaming completed successfully', 'performStreamingRequest', { chunkCount })
      onComplete()
    } catch (error) {
      throw new ServiceError(
        ServiceErrorCode.EXTERNAL_SERVICE_ERROR,
        `Streaming error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        { serviceName: this.serviceName, operation: 'performStreamingRequest', details: { chunkCount } }
      )
    }
  }
}

export const openRouterService = new OpenRouterService()
