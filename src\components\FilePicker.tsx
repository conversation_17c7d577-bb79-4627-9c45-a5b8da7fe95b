import React, { useState, useEffect } from 'react'
import { X, Search, FileText, Image, File, Folder } from './Icons'
import { FileRecord } from '../types'
import { sharedDropboxService } from '../services/sharedDropboxService'
import { contextVaultService } from '../services/contextVaultService'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faList, faThLarge, faFolder, faArrowLeft } from '@fortawesome/free-solid-svg-icons'

interface FilePickerProps {
  isOpen: boolean
  onClose: () => void
  onFileSelect: (files: FileRecord[]) => void
  mode: 'files' | 'images'
}

const FilePicker: React.FC<FilePickerProps> = ({
  isOpen,
  onClose,
  onFileSelect,
  mode
}) => {
  const [files, setFiles] = useState<FileRecord[]>([])
  const [filteredFiles, setFilteredFiles] = useState<FileRecord[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedFiles, setSelectedFiles] = useState<Set<string>>(new Set())
  const [isLoading, setIsLoading] = useState(false)
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [currentPath, setCurrentPath] = useState<string>('')
  const [pathHistory, setPathHistory] = useState<string[]>([])
  const [autoParseEnabled, setAutoParseEnabled] = useState(true)


  useEffect(() => {
    if (isOpen) {
      // Only load files when actually needed
      loadAndFilterFiles()
    }
  }, [isOpen, searchQuery, mode, currentPath]) // React to search, mode, and path changes

  const loadAndFilterFiles = async () => {
    console.log('Loading files for FilePicker, mode:', mode, 'searchQuery:', searchQuery)
    setIsLoading(true)
    try {
      let results: FileRecord[] = []

      // Get current context selection
      const selectedContext = contextVaultService.getSelectedContext()

      if (selectedContext) {
        // Context vault selected - scan the specific vault's root folder
        console.log('Loading files from context vault root folder:', selectedContext.name)

        if (window.electronAPI?.vault) {
          try {
            // Get the context's root path and scan for files
            const basePath = selectedContext.path
            const scanPath = currentPath ? `${basePath}/${currentPath}` : basePath

            // Check if scanFolder method exists (requires app restart after adding new IPC handlers)
            if (typeof window.electronAPI.vault.scanFolder !== 'function') {
              console.warn('scanFolder method not available - please restart the Electron app to load new IPC handlers')
              return
            }

            const scanResult = await window.electronAPI.vault.scanFolder(scanPath)

            if (scanResult.success && scanResult.files) {
              // Convert vault files to FileRecord format
              results = scanResult.files.map((file: any) => {
                const getFileType = (filename: string, isDirectory: boolean) => {
                  if (isDirectory) return 'folder'
                  const ext = filename.toLowerCase().split('.').pop() || ''
                  if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].includes(ext)) return 'image'
                  if (['pdf'].includes(ext)) return 'pdf'
                  if (['doc', 'docx'].includes(ext)) return 'word'
                  if (['xls', 'xlsx'].includes(ext)) return 'excel'
                  if (['ppt', 'pptx'].includes(ext)) return 'powerpoint'
                  if (['txt', 'md', 'markdown'].includes(ext)) return 'text'
                  return 'document'
                }

                return {
                  id: file.name + '_' + file.lastModified,
                  filename: file.name,
                  file_path: file.path,
                  file_type: getFileType(file.name, file.isDirectory),
                  file_size: file.size || 0,
                  file_hash: '',
                  indexed_at: file.lastModified || new Date().toISOString(),
                  extracted_content: null,
                  metadata: {
                    uploaded_via: 'context_vault',
                    context_id: selectedContext.id,
                    is_directory: file.isDirectory
                  }
                }
              })

              // Apply search filter if provided
              if (searchQuery) {
                results = results.filter(file =>
                  file.filename.toLowerCase().includes(searchQuery.toLowerCase())
                )
              }
            }
          } catch (error) {
            console.error('Error scanning context vault folder:', error)
          }
        }
      } else {
        // No context selected - scan shared dropbox folder directly
        console.log('Loading files from shared dropbox folder')

        if (window.electronAPI?.vault) {
          try {
            // Get shared dropbox path
            const destination = await sharedDropboxService.getUploadDestination()
            console.log('Shared dropbox destination:', destination)

            if (destination.type === 'shared' && destination.path) {
              // Check if scanFolder method exists (requires app restart after adding new IPC handlers)
              if (typeof window.electronAPI.vault.scanFolder !== 'function') {
                console.warn('scanFolder method not available - please restart the Electron app to load new IPC handlers')
                return
              }

              // Handle current path for shared dropbox navigation
              const scanPath = currentPath ? `${destination.path}/${currentPath}` : destination.path

              // Scan the shared dropbox folder
              const scanResult = await window.electronAPI.vault.scanFolder(scanPath)

              if (scanResult.success && scanResult.files) {
                // Convert vault files to FileRecord format
                results = scanResult.files.map((file: any) => {
                  const getFileType = (filename: string, isDirectory: boolean) => {
                    if (isDirectory) return 'folder'
                    const ext = filename.toLowerCase().split('.').pop() || ''
                    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].includes(ext)) return 'image'
                    if (['pdf'].includes(ext)) return 'pdf'
                    if (['doc', 'docx'].includes(ext)) return 'word'
                    if (['xls', 'xlsx'].includes(ext)) return 'excel'
                    if (['ppt', 'pptx'].includes(ext)) return 'powerpoint'
                    if (['txt', 'md', 'markdown'].includes(ext)) return 'text'
                    return 'document'
                  }

                  return {
                    id: file.name + '_' + file.lastModified,
                    filename: file.name,
                    file_path: file.path,
                    file_type: getFileType(file.name, file.isDirectory),
                    file_size: file.size || 0,
                    file_hash: '',
                    indexed_at: file.lastModified || new Date().toISOString(),
                    extracted_content: null,
                    metadata: {
                      uploaded_via: 'shared_dropbox',
                      is_directory: file.isDirectory
                    }
                  }
                })

                // Apply search filter if provided
                if (searchQuery) {
                  results = results.filter(file =>
                    file.filename.toLowerCase().includes(searchQuery.toLowerCase())
                  )
                }
              }
            }
          } catch (error) {
            console.error('Error scanning shared dropbox folder:', error)
          }
        }
      }

      console.log('Search results:', results.length, 'files')

      // Filter by mode (client-side for better UX)
      let filtered = results
      if (mode === 'images') {
        filtered = results.filter(file => file.file_type === 'image')
      } else {
        filtered = results.filter(file => file.file_type !== 'image')
      }
      console.log('Filtered results:', filtered.length, 'files')

      setFiles(results) // Keep original for reference
      setFilteredFiles(filtered)
    } catch (error) {
      console.error('Error loading files:', error)
      setFilteredFiles([])
    } finally {
      setIsLoading(false)
    }
  }



  const handleFileClick = (file: FileRecord) => {
    if (file.metadata?.is_directory || file.file_type === 'folder') {
      // Navigate into folder
      navigateToFolder(file.filename)
    } else {
      // Toggle file selection
      toggleFileSelection(file.id)

      // Auto-parse if enabled
      if (autoParseEnabled) {
        handleAutoParseFile(file)
      }
    }
  }

  const toggleFileSelection = (fileId: string) => {
    console.log('Toggling file selection for:', fileId)
    const newSelection = new Set(selectedFiles)
    if (newSelection.has(fileId)) {
      newSelection.delete(fileId)
      console.log('Deselected file:', fileId)
    } else {
      newSelection.add(fileId)
      console.log('Selected file:', fileId)
    }
    setSelectedFiles(newSelection)
    console.log('Total selected files:', newSelection.size)
  }

  const navigateToFolder = (folderName: string) => {
    const newPath = currentPath ? `${currentPath}/${folderName}` : folderName
    setPathHistory([...pathHistory, currentPath])
    setCurrentPath(newPath)
    setSelectedFiles(new Set()) // Clear selections when navigating
  }

  const navigateBack = () => {
    if (pathHistory.length > 0) {
      const previousPath = pathHistory[pathHistory.length - 1]
      setCurrentPath(previousPath)
      setPathHistory(pathHistory.slice(0, -1))
      setSelectedFiles(new Set()) // Clear selections when navigating
    }
  }

  const handleAutoParseFile = async (file: FileRecord) => {
    console.log('Auto-parsing file:', file.filename, 'type:', file.file_type)

    try {
      // Check if file needs processing based on type and extension
      const fileExtension = file.filename.toLowerCase().split('.').pop() || ''
      const needsProcessing = [
        'pdf', 'document', 'text', 'markdown', 'word', 'excel', 'powerpoint'
      ].includes(file.file_type) || [
        'pdf', 'doc', 'docx', 'txt', 'md', 'xls', 'xlsx', 'ppt', 'pptx'
      ].includes(fileExtension)

      console.log('File extension:', fileExtension, 'Needs processing:', needsProcessing)

      if (needsProcessing && window.electronAPI?.files) {
        console.log('Starting auto-parse for:', file.filename)

        // Use appropriate indexing method based on file source
        let result
        if (file.metadata?.uploaded_via === 'context_vault') {
          // For vault files, use vault-aware indexing
          const currentContext = contextVaultService.getSelectedContext()
          if (currentContext) {
            const vaultPath = currentContext.path
            const relativePath = file.file_path.replace(vaultPath + '/', '').replace(vaultPath + '\\', '')
            result = await window.electronAPI.files.indexVaultFile(file.file_path, currentContext.name, relativePath, true)
          } else {
            console.warn('No context selected for vault file auto-parse')
            return
          }
        } else {
          // For legacy files, use regular indexing
          result = await window.electronAPI.files.indexFile(file.file_path, true)
        }

        if (result.success) {
          console.log('Auto-parse completed for:', file.filename)
          // File is now indexed and processed
        } else {
          console.warn('Auto-parse failed for:', file.filename, result.error)
        }
      } else {
        console.log('File type does not need processing:', file.file_type, 'extension:', fileExtension)
      }
    } catch (error) {
      console.error('Error during auto-parse:', error)
    }
  }

  const handleTestDirectParsing = async (file: FileRecord) => {
    try {
      console.log('Testing direct parsing for:', file.filename)
      const result = await window.electronAPI.files.testDirectParsing(file.file_path)
      console.log('Direct parsing test result:', result)

      if (result.success) {
        console.log('✅ Direct parsing successful!')
        console.log('File type:', result.fileType)
        console.log('Text length:', result.textLength)
        console.log('First 500 chars:', result.firstChars)
      } else {
        console.log('❌ Direct parsing failed:', result.error)
      }
    } catch (error) {
      console.error('Error during direct parsing test:', error)
    }
  }

  const handleSelectFiles = async () => {
    const selectedFileRecords = files.filter(file => selectedFiles.has(file.id))
    console.log('Selected files for attachment:', selectedFileRecords)

    // For vault files, we need to index them first to get proper database IDs
    const processedFiles = []

    for (const file of selectedFileRecords) {
      if (file.metadata?.uploaded_via === 'context_vault' || file.metadata?.uploaded_via === 'shared_dropbox') {
        try {
          console.log('Indexing vault file for database:', file.filename)
          console.log('File path:', file.file_path)
          console.log('Auto-parse enabled:', autoParseEnabled)

          // For vault files, we need to use the new vault-aware indexing
          if (file.metadata?.uploaded_via === 'context_vault') {
            // Get the current selected context from the context vault service
            const currentContext = contextVaultService.getSelectedContext()

            if (currentContext) {
              // Calculate relative path from vault root
              const vaultPath = currentContext.path
              const relativePath = file.file_path.replace(vaultPath + '/', '').replace(vaultPath + '\\', '')

              console.log('Vault indexing:', {
                vaultName: currentContext.name,
                relativePath: relativePath,
                fullPath: file.file_path
              })

              // Use the new vault-aware indexing method
              const indexResult = await window.electronAPI.files.indexVaultFile(
                file.file_path,
                currentContext.name,
                relativePath,
                autoParseEnabled
              )

              console.log('Vault index result:', indexResult)

              if (indexResult && indexResult.success && indexResult.file) {
                // Use the indexed file with proper database ID
                processedFiles.push(indexResult.file)
                console.log('Vault file indexed successfully:', file.filename, 'ID:', indexResult.file.id)
              } else {
                console.warn('Failed to index vault file:', file.filename, 'Error:', indexResult.error)
                // Fallback to original file
                processedFiles.push(file)
              }
            } else {
              console.warn('No context selected for vault file, using legacy indexing:', file.filename)
              // Fallback to legacy indexing if no context is selected
              const indexResult = await window.electronAPI.files.indexFile(file.file_path, autoParseEnabled)

              if (indexResult && typeof indexResult === 'string') {
                const indexedFile = { ...file, id: indexResult }
                processedFiles.push(indexedFile)
              } else {
                processedFiles.push(file)
              }
            }
          } else {
            // For shared dropbox files, use legacy indexing
            const indexResult = await window.electronAPI.files.indexFile(file.file_path, autoParseEnabled)

            console.log('Legacy index result:', indexResult)

            if (indexResult && typeof indexResult === 'string') {
              // indexFile returns a string ID on success
              console.log('File indexed successfully with ID:', indexResult)
              // Create a new file record with the database ID
              const indexedFile = { ...file, id: indexResult }
              processedFiles.push(indexedFile)
            } else {
              console.warn('Failed to index file:', file.filename, 'Result:', indexResult)
              // Fallback to original file
              processedFiles.push(file)
            }
          }
        } catch (error) {
          console.error('Error indexing vault file:', file.filename, 'Error:', error)
          // Fallback to original file
          processedFiles.push(file)
        }
      } else {
        // Legacy file, use as-is
        processedFiles.push(file)
      }
    }

    console.log('Processed files for attachment:', processedFiles)
    onFileSelect(processedFiles)
    onClose()
  }

  const handleBrowseFiles = async () => {
    try {
      if (window.electronAPI?.files) {
        const result = await window.electronAPI.files.showOpenDialog({
          title: mode === 'images' ? 'Select Images' : 'Select Files',
          properties: ['openFile', 'multiSelections'],
          filters: mode === 'images' 
            ? [
                { name: 'Images', extensions: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'] },
                { name: 'All Files', extensions: ['*'] }
              ]
            : [
                { name: 'Documents', extensions: ['pdf', 'doc', 'docx', 'txt', 'md'] },
                { name: 'Spreadsheets', extensions: ['xls', 'xlsx', 'csv'] },
                { name: 'Presentations', extensions: ['ppt', 'pptx'] },
                { name: 'All Files', extensions: ['*'] }
              ]
        })

        if (!result.canceled && result.filePaths.length > 0) {
          const fileIds = await Promise.all(
            result.filePaths.map(p => window.electronAPI.files.indexFile(p))
          );
          const allFiles = await window.electronAPI.files.getIndexedFiles();
          const selectedFiles = allFiles.filter(f => fileIds.includes(f.id));

          // Ensure the files are in the correct format for onFileSelect
          const fileRecords: FileRecord[] = selectedFiles.map(file => ({
            id: file.id,
            filename: file.filename,
            file_path: file.file_path,
            file_type: file.file_type,
            file_size: file.file_size,
            file_hash: file.file_hash,
            indexed_at: file.indexed_at,
            extracted_content: file.extracted_content,
            metadata: file.metadata,
          }));

          onFileSelect(fileRecords);
          onClose();
        }
      }
    } catch (error) {
      console.error('Error browsing files:', error)
    }
  }

  const getFileIcon = (fileType: string, isDirectory?: boolean) => {
    if (isDirectory || fileType === 'folder') {
      return <FontAwesomeIcon icon={faFolder} className="h-4 w-4 text-yellow-500" />
    }

    switch (fileType) {
      case 'image':
        return <Image className="h-4 w-4 text-blue-400" />
      case 'pdf':
        return <FileText className="h-4 w-4 text-red-400" />
      case 'word':
        return <FileText className="h-4 w-4 text-blue-600" />
      case 'excel':
        return <FileText className="h-4 w-4 text-green-600" />
      case 'powerpoint':
        return <FileText className="h-4 w-4 text-orange-600" />
      case 'text':
      case 'markdown':
        return <FileText className="h-4 w-4 text-neutral-400" />
      default:
        return <File className="h-4 w-4 text-neutral-400" />
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-neutral-900 border border-neutral-800 rounded-lg shadow-xl w-full max-w-4xl h-[80vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-neutral-800">
          <div>
            <h2 className="text-xl font-semibold">
              {mode === 'images' ? 'Select Images' : 'Select Files'}
            </h2>
            <p className="text-sm text-neutral-400 mt-1">
              Click on files to select them, then click "Attach Files" to add them to your message
            </p>
          </div>
          <button
            onClick={onClose}
            className="h-8 w-8 flex items-center justify-center rounded-lg hover:bg-neutral-800 transition-colors"
          >
            <X className="h-4 w-4" />
          </button>
        </div>

        {/* Search, Browse, and Controls */}
        <div className="p-6 border-b border-neutral-800">
          {/* Navigation Breadcrumbs */}
          {currentPath && (
            <div className="flex items-center gap-2 mb-4 text-sm text-neutral-400">
              <button
                onClick={navigateBack}
                className="flex items-center gap-1 px-2 py-1 hover:bg-neutral-700 rounded transition-colors"
              >
                <FontAwesomeIcon icon={faArrowLeft} className="h-3 w-3" />
                Back
              </button>
              <span>/</span>
              <span className="text-neutral-300">{currentPath}</span>
            </div>
          )}

          <div className="flex gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-neutral-500" />
              <input
                type="text"
                placeholder="Search files..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full bg-neutral-800 border border-neutral-700 rounded-lg pl-10 pr-4 py-2 text-sm focus:ring-2 focus:ring-indigo-500 focus:border-transparent outline-none"
              />
            </div>

            {/* View Mode Toggle */}
            <div className="flex border border-neutral-700 rounded-lg overflow-hidden">
              <button
                onClick={() => setViewMode('grid')}
                className={`px-3 py-2 text-sm transition-colors ${
                  viewMode === 'grid'
                    ? 'bg-indigo-500 text-white'
                    : 'bg-neutral-800 text-neutral-400 hover:bg-neutral-700'
                }`}
                title="Grid view"
              >
                <FontAwesomeIcon icon={faThLarge} className="h-4 w-4" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`px-3 py-2 text-sm transition-colors ${
                  viewMode === 'list'
                    ? 'bg-indigo-500 text-white'
                    : 'bg-neutral-800 text-neutral-400 hover:bg-neutral-700'
                }`}
                title="List view"
              >
                <FontAwesomeIcon icon={faList} className="h-4 w-4" />
              </button>
            </div>

            <button
              onClick={handleBrowseFiles}
              className="px-4 py-2 bg-neutral-700 hover:bg-neutral-600 text-neutral-200 rounded-lg text-sm font-medium transition-colors"
            >
              Browse
            </button>
            <button
              onClick={() => {
                console.log('Confirm button clicked, selected count:', selectedFiles.size)
                handleSelectFiles()
              }}
              disabled={selectedFiles.size === 0}
              className={`
                px-4 py-2 rounded-lg text-sm font-medium transition-colors
                ${selectedFiles.size > 0
                  ? 'bg-indigo-500 hover:bg-indigo-600 text-white shadow-lg shadow-indigo-500/25'
                  : 'bg-neutral-700 text-neutral-500 cursor-not-allowed'
                }
              `}
            >
              Confirm ({selectedFiles.size})
            </button>
          </div>

          {/* Auto-parse toggle and selection info */}
          <div className="flex items-center justify-between mt-4">
            <label className="flex items-center gap-2 text-sm text-neutral-400">
              <input
                type="checkbox"
                checked={autoParseEnabled}
                onChange={(e) => setAutoParseEnabled(e.target.checked)}
                className="w-4 h-4 text-indigo-500 bg-neutral-800 border-neutral-600 rounded focus:ring-indigo-500 focus:ring-2"
              />
              Auto-parse files when selected
            </label>

            {selectedFiles.size > 0 && (
              <div className="text-sm text-indigo-400">
                {selectedFiles.size} file{selectedFiles.size !== 1 ? 's' : ''} selected
              </div>
            )}
          </div>
        </div>

        {/* File List */}
        <div className="flex-1 overflow-y-auto p-6">
          {isLoading ? (
            <div className="flex items-center justify-center h-32">
              <div className="text-neutral-400">Loading files...</div>
            </div>
          ) : filteredFiles.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-32 text-neutral-400">
              <Folder className="h-8 w-8 mb-2" />
              <p>No {mode === 'images' ? 'images' : 'files'} found</p>
              <p className="text-sm mt-1">Try browsing for files or check your Chatlo folder</p>
            </div>
          ) : viewMode === 'grid' ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredFiles.map((file) => (
                <div
                  key={file.id}
                  onClick={() => handleFileClick(file)}
                  className={`
                    p-4 border-2 rounded-lg cursor-pointer transition-all relative
                    ${file.metadata?.is_directory || file.file_type === 'folder'
                      ? 'border-yellow-500/50 hover:border-yellow-400 hover:bg-yellow-500/10'
                      : selectedFiles.has(file.id)
                        ? 'border-indigo-500 bg-indigo-500/20 shadow-lg shadow-indigo-500/25'
                        : 'border-neutral-700 hover:border-indigo-400 hover:bg-neutral-800/50'
                    }
                  `}
                >
                  {selectedFiles.has(file.id) && !(file.metadata?.is_directory || file.file_type === 'folder') && (
                    <div className="absolute top-2 right-2 flex gap-2">
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          handleTestDirectParsing(file)
                        }}
                        className="px-2 py-1 text-xs bg-yellow-500/20 text-yellow-400 rounded hover:bg-yellow-500/30 transition-colors"
                        title="Test direct parsing"
                      >
                        Test
                      </button>
                      <div className="w-6 h-6 bg-indigo-500 rounded-full flex items-center justify-center">
                        <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                    </div>
                  )}
                  <div className="flex items-start gap-3">
                    {getFileIcon(file.file_type, file.metadata?.is_directory)}
                    <div className="flex-1 min-w-0">
                      <h3 className="font-medium text-sm truncate">{file.filename}</h3>
                      <p className="text-xs text-neutral-500 mt-1">
                        {file.metadata?.is_directory || file.file_type === 'folder'
                          ? 'Folder'
                          : `${formatFileSize(file.file_size)} • ${file.file_type}`
                        }
                      </p>
                      <p className="text-xs text-neutral-600 mt-1">
                        {new Date(file.indexed_at).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            /* List View */
            <div className="space-y-1">
              {filteredFiles.map((file) => (
                <div
                  key={file.id}
                  onClick={() => handleFileClick(file)}
                  className={`
                    flex items-center gap-3 p-3 rounded-lg cursor-pointer transition-all
                    ${file.metadata?.is_directory || file.file_type === 'folder'
                      ? 'hover:bg-yellow-500/10 border-l-2 border-l-yellow-500/50'
                      : selectedFiles.has(file.id)
                        ? 'bg-indigo-500/20 border-l-2 border-l-indigo-500'
                        : 'hover:bg-neutral-800/50 border-l-2 border-l-transparent'
                    }
                  `}
                >
                  {getFileIcon(file.file_type, file.metadata?.is_directory)}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2">
                      <h3 className="font-medium text-sm truncate">{file.filename}</h3>
                      {selectedFiles.has(file.id) && !(file.metadata?.is_directory || file.file_type === 'folder') && (
                        <div className="w-4 h-4 bg-indigo-500 rounded-full flex items-center justify-center flex-shrink-0">
                          <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        </div>
                      )}
                      {selectedFiles.has(file.id) && !(file.metadata?.is_directory || file.file_type === 'folder') && (
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            handleTestDirectParsing(file)
                          }}
                          className="px-2 py-1 text-xs bg-yellow-500/20 text-yellow-400 rounded hover:bg-yellow-500/30 transition-colors"
                          title="Test direct parsing"
                        >
                          Test Parse
                        </button>
                      )}
                    </div>
                    <div className="flex items-center gap-4 text-xs text-neutral-500 mt-1">
                      <span>
                        {file.metadata?.is_directory || file.file_type === 'folder'
                          ? 'Folder'
                          : `${formatFileSize(file.file_size)} • ${file.file_type}`
                        }
                      </span>
                      <span>{new Date(file.indexed_at).toLocaleDateString()}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>


      </div>
    </div>
  )
}

export default FilePicker
