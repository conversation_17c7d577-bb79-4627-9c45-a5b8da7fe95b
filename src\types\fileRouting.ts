/**
 * File Routing System Types
 * Defines interfaces for the unified file handling system
 */

export interface FileDestination {
  contextId?: string
  path?: string
  autoRoute?: boolean
}

export interface FileUploadRequest {
  token?: string
  files: File[]
  destination: FileDestination
  options?: FileUploadOptions
}

export interface FileUploadOptions {
  overwrite?: boolean
  generateThumbnails?: boolean
  extractText?: boolean
  customOptions?: Record<string, any>
}

export interface UploadedFile {
  originalName: string
  savedPath: string
  size: number
  mimeType: string
  metadata?: FileMetadata
  thumbnail?: string
  extractedText?: string
  id: string
  contextId?: string
}

export interface FileUploadResponse {
  success: boolean
  files: UploadedFile[]
  errors?: FileError[]
}

export interface FileError {
  filename: string
  message: string
  code?: string
}

export interface FileMetadata {
  width?: number
  height?: number
  format?: string
  duration?: number
  pages?: number
  author?: string
  title?: string
  createdDate?: string
  modifiedDate?: string
  [key: string]: any
}

export interface FileReadRequest {
  token?: string
  filePath: string
  options?: FileReadOptions
}

export interface FileReadOptions {
  encoding?: string
  range?: { start: number; end: number }
}

export interface FileReadResponse {
  success: boolean
  content?: string | Buffer
  metadata?: FileMetadata
  error?: string
}

export interface FileListRequest {
  token?: string
  path: string
  options?: FileListOptions
}

export interface FileListOptions {
  recursive?: boolean
  includeHidden?: boolean
  filter?: FileFilter
}

export interface FileFilter {
  extensions?: string[]
  mimeTypes?: string[]
  maxSize?: number
  minSize?: number
}

export interface FileInfo {
  name: string
  path: string
  size: number
  mimeType: string
  isDirectory: boolean
  modified: string
  created?: string
  metadata?: FileMetadata
}

export interface FileListResponse {
  success: boolean
  files: FileInfo[]
  totalCount: number
  error?: string
}

export interface FileDeleteRequest {
  token?: string
  filePaths: string[]
  options?: FileDeleteOptions
}

export interface FileDeleteOptions {
  moveToTrash?: boolean
  force?: boolean
}

export interface FileDeleteResponse {
  success: boolean
  deletedFiles: string[]
  errors?: FileError[]
}

export interface FileOperation {
  type: 'upload' | 'delete' | 'move' | 'copy' | 'process'
  source?: string
  destination?: string
  files?: File[]
  options?: Record<string, any>
}

export interface BatchOperationRequest {
  token?: string
  operations: FileOperation[]
}

export interface BatchOperationResponse {
  success: boolean
  results: Array<{
    operation: FileOperation
    success: boolean
    result?: any
    error?: string
  }>
}

export interface FileValidationError {
  filename: string
  error: 'FILE_TOO_LARGE' | 'UNSUPPORTED_TYPE' | 'INVALID_FILENAME' | 'SECURITY_RISK'
  message: string
}

export interface ValidationResult {
  valid: boolean
  errors: FileValidationError[]
}

export interface FileRoutingRule {
  pattern: string
  destination: string
  priority?: number
}

export interface FileRoutingConfig {
  maxFileSize: number
  allowedTypes: string[]
  autoRouting: {
    enabled: boolean
    rules: FileRoutingRule[]
  }
  thumbnails: {
    enabled: boolean
    maxSize: { width: number; height: number }
    quality: number
  }
  textExtraction: {
    enabled: boolean
    maxFileSize: number
  }
  security: {
    scanFiles: boolean
    quarantineUnsafe: boolean
  }
}

export interface ProcessingOptions {
  destinationPath: string
  generateThumbnails?: boolean
  extractText?: boolean
  customOptions?: Record<string, any>
}

export interface ProcessedFile {
  originalName: string
  savedPath: string
  size: number
  mimeType: string
  metadata?: FileMetadata
  thumbnail?: string
  extractedText?: string
}

// Plugin-related types
export interface FileProcessorPlugin {
  id: string
  name: string
  supportedMimeTypes: string[]
  supportedExtensions: string[]
  processFile(file: File, options: ProcessingOptions): Promise<ProcessedFile>
  extractMetadata?(file: File): Promise<FileMetadata>
  generateThumbnail?(file: File): Promise<Buffer>
}

export interface OperationMetrics {
  count: number
  totalSize: number
  averageTime: number
  errors: number
}

export interface UsageStats {
  totalOperations: number
  totalSize: number
  totalErrors: number
  errorRate: number
}

export interface FileOperationEvent {
  type: 'upload' | 'delete' | 'move' | 'copy' | 'process'
  contextId?: string
  fileSize?: number
  duration: number
  error?: string
}
