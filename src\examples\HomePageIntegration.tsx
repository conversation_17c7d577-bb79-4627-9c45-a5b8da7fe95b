/**
 * Example: HomePage Integration with FileRoutingService
 * Shows how to replace SharedDropboxService with the new unified system
 */

import React, { useState, useCallback } from 'react'
import { ContextDropZone } from '../components/UnifiedDropZone'
import { useFileOperations } from '../hooks/useFileOperations'
import { ContextVaultCard, UploadedFile } from '../types'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faUpload } from '@fortawesome/free-solid-svg-icons'

interface HomePageProps {
  contexts: ContextVaultCard[]
  onContextUpdate?: () => void
}

export const HomePageWithFileRouting: React.FC<HomePageProps> = ({
  contexts,
  onContextUpdate
}) => {
  const [uploadingContexts, setUploadingContexts] = useState<Set<string>>(new Set())
  const { state } = useFileOperations()

  /**
   * Handle successful file upload to context
   */
  const handleUploadComplete = useCallback(async (contextId: string, files: UploadedFile[]) => {
    console.log(`${files.length} files successfully added to context ${contextId}`)
    
    // Remove from uploading set
    setUploadingContexts(prev => {
      const next = new Set(prev)
      next.delete(contextId)
      return next
    })
    
    // Refresh vault data to reflect new file counts
    onContextUpdate?.()
  }, [onContextUpdate])

  /**
   * Handle upload error
   */
  const handleUploadError = useCallback((contextId: string, error: string) => {
    console.error(`Error uploading files to context ${contextId}:`, error)
    
    // Remove from uploading set
    setUploadingContexts(prev => {
      const next = new Set(prev)
      next.delete(contextId)
      return next
    })
    
    // Show user-friendly error message
    // In real implementation, you'd use a toast/notification system
    alert(`Upload failed: ${error}`)
  }, [])

  /**
   * Handle upload progress
   */
  const handleUploadProgress = useCallback((contextId: string, progress: number) => {
    // Add to uploading set when upload starts
    if (progress > 0 && progress < 100) {
      setUploadingContexts(prev => new Set(prev).add(contextId))
    }
  }, [])

  /**
   * Render context card with integrated drop zone
   */
  const renderContextCard = (context: ContextVaultCard) => {
    const isUploading = uploadingContexts.has(context.id)
    
    return (
      <div key={context.id} className="context-card">
        {/* Main card content */}
        <div className="card-header">
          <h3>{context.name}</h3>
          <p>{context.description}</p>
        </div>
        
        <div className="card-stats">
          <span>{context.fileCount} files</span>
          <span>{context.conversationCount} conversations</span>
        </div>
        
        {/* Integrated drop zone */}
        <ContextDropZone
          contextId={context.id}
          className="context-drop-zone"
          dragActiveClassName="drag-active"
          dragOverClassName="drag-over"
          onUploadComplete={(files) => handleUploadComplete(context.id, files)}
          onUploadError={(error) => handleUploadError(context.id, error)}
          onUploadProgress={(progress) => handleUploadProgress(context.id, progress)}
          showProgress={true}
          disabled={isUploading}
        >
          <div className="drop-zone-content">
            {isUploading ? (
              <div className="uploading-state">
                <div className="spinner" />
                <p>Uploading...</p>
              </div>
            ) : (
              <div className="default-state">
                <FontAwesomeIcon icon={faUpload} className="upload-icon" />
                <p>Drop files → /documents</p>
              </div>
            )}
          </div>
        </ContextDropZone>
      </div>
    )
  }

  return (
    <div className="home-page">
      <div className="contexts-grid">
        {contexts.map(renderContextCard)}
      </div>
      
      {/* Global upload status */}
      {state.uploading && (
        <div className="global-upload-status">
          <p>Uploading files...</p>
          {Object.entries(state.uploadProgress).map(([filename, progress]) => (
            <div key={filename} className="file-progress">
              <span>{filename}</span>
              <div className="progress-bar">
                <div 
                  className="progress-fill" 
                  style={{ width: `${progress}%` }}
                />
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

/**
 * Example: Migration helper component
 * Shows how to gradually migrate from SharedDropboxService
 */
export const MigrationExample: React.FC = () => {
  const [useNewSystem, setUseNewSystem] = useState(false)
  
  return (
    <div className="migration-example">
      <div className="toggle-container">
        <label>
          <input
            type="checkbox"
            checked={useNewSystem}
            onChange={(e) => setUseNewSystem(e.target.checked)}
          />
          Use new FileRoutingService
        </label>
      </div>
      
      {useNewSystem ? (
        <ContextDropZone
          contextId="example-context"
          className="new-drop-zone"
          onUploadComplete={(files) => {
            console.log('New system uploaded:', files)
          }}
          onUploadError={(error) => {
            console.error('New system error:', error)
          }}
        >
          <div className="drop-zone-content">
            <p>✨ New FileRoutingService</p>
            <p>Drop files here</p>
          </div>
        </ContextDropZone>
      ) : (
        <div className="old-drop-zone">
          <p>🔧 Old SharedDropboxService</p>
          <p>Legacy file handling</p>
        </div>
      )}
    </div>
  )
}

/**
 * Example: Advanced drop zone with custom validation
 */
export const AdvancedDropZoneExample: React.FC = () => {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([])
  
  return (
    <div className="advanced-example">
      <h3>Advanced File Upload</h3>
      
      <ContextDropZone
        contextId="advanced-context"
        className="advanced-drop-zone"
        acceptedTypes={['image/*', 'application/pdf']}
        maxFiles={5}
        showProgress={true}
        showFileList={true}
        onUploadComplete={(files) => {
          setUploadedFiles(prev => [...prev, ...files])
        }}
        onUploadError={(error) => {
          console.error('Upload error:', error)
        }}
        onFilesSelected={(files) => {
          console.log('Files selected:', files.map(f => f.name))
        }}
      >
        <div className="custom-drop-content">
          <div className="upload-icon">📎</div>
          <h4>Upload Images or PDFs</h4>
          <p>Maximum 5 files</p>
          <p>Drag & drop or click to select</p>
        </div>
      </ContextDropZone>
      
      {uploadedFiles.length > 0 && (
        <div className="uploaded-files-list">
          <h4>Uploaded Files:</h4>
          <ul>
            {uploadedFiles.map((file, index) => (
              <li key={index}>
                <strong>{file.originalName}</strong>
                <br />
                <small>
                  {file.mimeType} • {(file.size / 1024).toFixed(1)} KB
                  <br />
                  Saved to: {file.savedPath}
                </small>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  )
}

/**
 * Example CSS styles for the components
 */
export const exampleStyles = `
.context-card {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  margin: 8px;
  background: white;
}

.context-drop-zone {
  margin-top: 12px;
  border: 2px dashed #cbd5e0;
  border-radius: 6px;
  padding: 16px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.context-drop-zone:hover {
  border-color: #4299e1;
  background-color: #f7fafc;
}

.context-drop-zone.drag-active {
  border-color: #4299e1;
  background-color: #ebf8ff;
}

.context-drop-zone.drag-over {
  border-color: #3182ce;
  background-color: #bee3f8;
}

.context-drop-zone.uploading {
  border-color: #38a169;
  background-color: #f0fff4;
}

.context-drop-zone.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.upload-icon {
  font-size: 24px;
  color: #a0aec0;
  margin-bottom: 8px;
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #e2e8f0;
  border-top: 2px solid #4299e1;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.progress-bar {
  width: 100%;
  height: 4px;
  background-color: #e2e8f0;
  border-radius: 2px;
  overflow: hidden;
  margin: 8px 0;
}

.progress-fill {
  height: 100%;
  background-color: #4299e1;
  transition: width 0.3s ease;
}

.error-message {
  color: #e53e3e;
  font-size: 14px;
  margin-top: 8px;
  padding: 8px;
  background-color: #fed7d7;
  border-radius: 4px;
}

.global-upload-status {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  min-width: 300px;
}

.file-progress {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 8px 0;
}

.file-progress span {
  flex: 1;
  font-size: 14px;
  truncate: true;
}

.file-progress .progress-bar {
  flex: 2;
  margin: 0;
}
`
