/**
 * Base service class providing common functionality for all ChatLo services
 * Implements consistent patterns for initialization, error handling, and logging
 */

import { ServiceLogger, createServiceLogger, LogLevel, PerformanceLogger } from './ServiceLogger'
import { ServiceError, ServiceErrorCode, ServiceResult, wrapServiceOperation } from './ServiceError'

export interface ServiceConfig {
  name: string
  logLevel?: LogLevel
  enableConsoleLogging?: boolean
  autoInitialize?: boolean
}

export interface ServiceStatus {
  initialized: boolean
  healthy: boolean
  lastError?: ServiceError
  initializationTime?: number
  lastHealthCheck?: string
}

export abstract class BaseService {
  protected readonly serviceName: string
  protected readonly logger: ServiceLogger
  protected readonly performanceLogger: PerformanceLogger
  protected status: ServiceStatus
  private initializationPromise?: Promise<void>

  constructor(config: ServiceConfig) {
    this.serviceName = config.name
    this.logger = createServiceLogger(
      config.name,
      config.logLevel,
      config.enableConsoleLogging
    )
    this.performanceLogger = new PerformanceLogger(this.logger)
    
    this.status = {
      initialized: false,
      healthy: false
    }

    if (config.autoInitialize !== false) {
      // Auto-initialize by default, but allow opt-out
      this.initialize().catch(error => {
        this.logger.error('Auto-initialization failed', 'constructor', error)
      })
    }
  }

  /**
   * Initialize the service
   * This method should be implemented by subclasses
   */
  protected abstract doInitialize(): Promise<void>

  /**
   * Cleanup resources when service is destroyed
   * Override in subclasses if cleanup is needed
   */
  protected async doCleanup(): Promise<void> {
    // Default implementation does nothing
  }

  /**
   * Health check implementation
   * Override in subclasses for custom health checks
   */
  protected async doHealthCheck(): Promise<boolean> {
    return this.status.initialized
  }

  /**
   * Public initialization method with error handling and logging
   */
  public async initialize(): Promise<void> {
    if (this.status.initialized) {
      this.logger.debug('Service already initialized', 'initialize')
      return
    }

    if (this.initializationPromise) {
      this.logger.debug('Initialization already in progress, waiting...', 'initialize')
      return this.initializationPromise
    }

    this.initializationPromise = this.performInitialization()
    return this.initializationPromise
  }

  /**
   * Internal initialization logic with performance tracking
   */
  private async performInitialization(): Promise<void> {
    this.logger.operationStart('initialize')
    this.performanceLogger.startTimer('initialization')

    try {
      await this.doInitialize()
      
      const initTime = this.performanceLogger.endTimer('initialization')
      this.status = {
        initialized: true,
        healthy: true,
        initializationTime: initTime
      }
      
      this.logger.operationSuccess('initialize', { initializationTime: initTime })
    } catch (error) {
      const serviceError = ServiceError.fromError(error, this.serviceName, 'initialize')
      this.status = {
        initialized: false,
        healthy: false,
        lastError: serviceError
      }
      
      this.performanceLogger.endTimer('initialization')
      this.logger.operationFailure('initialize', serviceError)
      throw serviceError
    }
  }

  /**
   * Cleanup service resources
   */
  public async cleanup(): Promise<void> {
    this.logger.operationStart('cleanup')

    try {
      await this.doCleanup()
      this.status.initialized = false
      this.status.healthy = false
      this.logger.operationSuccess('cleanup')
    } catch (error) {
      const serviceError = ServiceError.fromError(error, this.serviceName, 'cleanup')
      this.logger.operationFailure('cleanup', serviceError)
      throw serviceError
    }
  }

  /**
   * Perform health check
   */
  public async healthCheck(): Promise<boolean> {
    try {
      const isHealthy = await this.doHealthCheck()
      this.status.healthy = isHealthy
      this.status.lastHealthCheck = new Date().toISOString()
      
      if (isHealthy) {
        this.logger.debug('Health check passed', 'healthCheck')
      } else {
        this.logger.warn('Health check failed', 'healthCheck')
      }
      
      return isHealthy
    } catch (error) {
      const serviceError = ServiceError.fromError(error, this.serviceName, 'healthCheck')
      this.status.healthy = false
      this.status.lastError = serviceError
      this.status.lastHealthCheck = new Date().toISOString()
      
      this.logger.error('Health check error', 'healthCheck', serviceError)
      return false
    }
  }

  /**
   * Get current service status
   */
  public getStatus(): ServiceStatus {
    return { ...this.status }
  }

  /**
   * Check if service is ready for operations
   */
  public isReady(): boolean {
    return this.status.initialized && this.status.healthy
  }

  /**
   * Wait for service to be ready
   */
  public async waitForReady(timeoutMs: number = 10000): Promise<void> {
    const startTime = Date.now()
    
    while (!this.isReady() && (Date.now() - startTime) < timeoutMs) {
      await new Promise(resolve => setTimeout(resolve, 100))
    }
    
    if (!this.isReady()) {
      throw new ServiceError(
        ServiceErrorCode.TIMEOUT_ERROR,
        `Service ${this.serviceName} did not become ready within ${timeoutMs}ms`,
        { serviceName: this.serviceName, operation: 'waitForReady' }
      )
    }
  }

  /**
   * Execute an operation with error handling and logging
   */
  protected async executeOperation<T>(
    operationName: string,
    operation: () => Promise<T>,
    logData?: any
  ): Promise<ServiceResult<T>> {
    this.logger.operationStart(operationName, logData)
    
    return await this.performanceLogger.measureAsync(operationName, async () => {
      const result = await wrapServiceOperation(operation, this.serviceName, operationName)
      
      if (result.success) {
        this.logger.operationSuccess(operationName, logData)
      } else {
        this.logger.operationFailure(operationName, result.error!, logData)
      }
      
      return result
    })
  }

  /**
   * Execute an operation and throw on error (for backward compatibility)
   */
  protected async executeOperationOrThrow<T>(
    operationName: string,
    operation: () => Promise<T>,
    logData?: any
  ): Promise<T> {
    const result = await this.executeOperation(operationName, operation, logData)
    
    if (!result.success) {
      throw result.error!
    }
    
    return result.data!
  }

  /**
   * Get service name
   */
  public getServiceName(): string {
    return this.serviceName
  }

  /**
   * Get logger instance for advanced usage
   */
  protected getLogger(): ServiceLogger {
    return this.logger
  }

  /**
   * Get performance logger instance
   */
  protected getPerformanceLogger(): PerformanceLogger {
    return this.performanceLogger
  }
}
