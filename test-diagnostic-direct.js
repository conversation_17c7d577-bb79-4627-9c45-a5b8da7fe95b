// Direct test of the diagnostic system
const { FileProcessingDiagnostics } = require('./dist/diagnostics/FileProcessingDiagnostics');
const { DatabaseManager } = require('./dist/database');
const { FileSystemManager } = require('./dist/fileSystem');

async function testDiagnostics() {
  try {
    console.log('Initializing diagnostic system...');
    
    // Initialize components
    const db = new DatabaseManager();
    const fileSystem = new FileSystemManager(db);
    
    // Create diagnostics instance
    const diagnostics = new FileProcessingDiagnostics(fileSystem, fileSystem.fileProcessor);
    
    // Test with a sample file path (you can change this to an actual PDF file)
    const testFilePath = 'C:\\Users\\<USER>\\Documents\\test.pdf';
    
    console.log(`Running diagnostics on: ${testFilePath}`);
    
    const result = await diagnostics.diagnoseFileProcessing(testFilePath);
    
    console.log('Diagnostic Results:');
    console.log(JSON.stringify(result, null, 2));
    
  } catch (error) {
    console.error('Error running diagnostic test:', error);
  }
}

testDiagnostics();
