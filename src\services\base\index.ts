/**
 * Base service infrastructure exports
 * Provides common functionality for all ChatLo services
 */

export { BaseService } from './BaseService'
export type { ServiceConfig, ServiceStatus } from './BaseService'

export { ServiceLogger, createServiceLogger, PerformanceLogger, LoggerConfig } from './ServiceLogger'
export { LogLevel } from './ServiceLogger'
export type { LogEntry } from './ServiceLogger'

export { ServiceError } from './ServiceError'
export { ServiceErrorCode } from './ServiceError'
export type { ServiceErrorContext, ServiceResult } from './ServiceError'
export { createSuccessResult, createErrorResult, wrapServiceOperation } from './ServiceError'
