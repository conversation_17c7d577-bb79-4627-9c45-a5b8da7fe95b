/**
 * Plugin-based File Processor Service
 * Modular file processing with optional plugins
 */

import * as path from 'path'
import { 
  FileProcessorPlugin, 
  ProcessedFileContent, 
  PluginManager, 
  PluginError,
  FileProcessorConfig 
} from './types'

export class PluginFileProcessorService {
  private pluginManager: PluginManager
  private initialized = false

  constructor(config?: Partial<FileProcessorConfig>) {
    this.pluginManager = new PluginManager(config)
  }

  // Initialize the service and load plugins
  async initialize(): Promise<void> {
    if (this.initialized) return

    try {
      // Load core plugins
      await this.loadCorePlugins()
      
      // Load optional plugins
      await this.loadOptionalPlugins()

      this.initialized = true
      console.log('PluginFileProcessorService initialized successfully')
    } catch (error) {
      console.error('Failed to initialize PluginFileProcessorService:', error)
      throw error
    }
  }

  // Load core plugins (always available)
  private async loadCorePlugins(): Promise<void> {
    const corePlugins = [
      () => import('./plugins/TextPlugin'),
      () => import('./plugins/MarkdownPlugin'),
      () => import('./plugins/BasicImagePlugin')
    ]

    for (const pluginLoader of corePlugins) {
      try {
        const module = await pluginLoader()
        const plugin = new module.default()
        await this.pluginManager.registerPlugin(plugin)
      } catch (error) {
        console.warn('Failed to load core plugin:', error)
        // Core plugins should not fail silently, but we continue
      }
    }
  }

  // Load optional plugins (graceful failure)
  private async loadOptionalPlugins(): Promise<void> {
    const optionalPlugins = [
      () => import('./plugins/PDFPlugin'),
      () => import('./plugins/WordPlugin'),
      () => import('./plugins/ExcelPlugin'),
      () => import('./plugins/PowerPointPlugin'),
      () => import('./plugins/ImagePlugin'),
      () => import('./plugins/OCRPlugin')
    ]

    for (const pluginLoader of optionalPlugins) {
      try {
        const module = await pluginLoader()
        const plugin = new module.default()
        await this.pluginManager.registerPlugin(plugin)
      } catch (error) {
        console.warn('Optional plugin not available:', error)
        // Optional plugins fail gracefully
      }
    }
  }

  // Main processing method
  async processFile(filePath: string, fileType: string): Promise<ProcessedFileContent> {
    if (!this.initialized) {
      await this.initialize()
    }

    try {
      // Get file extension
      const extension = path.extname(filePath).toLowerCase()

      // Find suitable plugins
      const typePlugins = this.pluginManager.getPluginsForType(fileType)
      const extensionPlugins = this.pluginManager.getPluginsForExtension(extension)
      
      // Combine and deduplicate plugins
      const allPlugins = [...typePlugins, ...extensionPlugins]
      const uniquePlugins = Array.from(new Set(allPlugins))

      // Filter enabled plugins and sort by priority
      const enabledPlugins = uniquePlugins
        .filter(plugin => {
          const config = this.pluginManager.getPluginConfig(plugin.name)
          return !config || config.enabled !== false
        })
        .filter(plugin => plugin.canProcess(filePath, fileType))
        .sort((a, b) => {
          const configA = this.pluginManager.getPluginConfig(a.name)
          const configB = this.pluginManager.getPluginConfig(b.name)
          const priorityA = configA?.priority || 0
          const priorityB = configB?.priority || 0
          return priorityB - priorityA // Higher priority first
        })

      if (enabledPlugins.length === 0) {
        return {
          error: `No suitable plugin found for file type: ${fileType} (${extension})`
        }
      }

      // Try plugins in order until one succeeds
      let lastError: Error | undefined
      for (const plugin of enabledPlugins) {
        try {
          console.log(`Processing ${filePath} with plugin: ${plugin.name}`)
          const result = await this.processWithTimeout(plugin, filePath)
          
          if (result.error && !plugin.optional) {
            // Non-optional plugin failed, try next
            lastError = new Error(result.error)
            continue
          }

          return result
        } catch (error) {
          console.warn(`Plugin ${plugin.name} failed:`, error)
          lastError = error as Error
          
          if (!plugin.optional) {
            // Non-optional plugin failed, try next
            continue
          }
        }
      }

      // All plugins failed
      return {
        error: `All plugins failed to process file: ${lastError?.message || 'Unknown error'}`
      }

    } catch (error) {
      console.error('Error in processFile:', error)
      return {
        error: `Failed to process file: ${(error as Error)?.message || 'Unknown error'}`
      }
    }
  }

  // Process with timeout
  private async processWithTimeout(
    plugin: FileProcessorPlugin, 
    filePath: string
  ): Promise<ProcessedFileContent> {
    const config = this.pluginManager.getPluginConfig(plugin.name)
    const timeout = config?.options?.timeout || 30000

    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        reject(new PluginError(`Plugin ${plugin.name} timed out`, plugin.name))
      }, timeout)

      plugin.process(filePath)
        .then(result => {
          clearTimeout(timer)
          resolve(result)
        })
        .catch(error => {
          clearTimeout(timer)
          reject(error)
        })
    })
  }

  // Batch process multiple files
  async processFiles(
    files: Array<{ filePath: string; fileType: string }>
  ): Promise<Array<ProcessedFileContent & { filePath: string }>> {
    const results = []

    for (const file of files) {
      const result = await this.processFile(file.filePath, file.fileType)
      results.push({
        ...result,
        filePath: file.filePath
      })
    }

    return results
  }

  // Check if file type is supported
  async isFileTypeSupported(fileType: string): Promise<boolean> {
    if (!this.initialized) {
      await this.initialize()
    }
    return this.pluginManager.isTypeSupported(fileType)
  }

  // Synchronous version for backward compatibility
  isFileTypeSupportedSync(fileType: string): boolean {
    // If not initialized, we can't accurately check support
    if (!this.initialized) {
      console.warn('PluginFileProcessorService not initialized, cannot check file type support accurately')
      return false
    }
    return this.pluginManager.isTypeSupported(fileType)
  }

  // Get supported file types
  getSupportedFileTypes(): string[] {
    const plugins = this.pluginManager.getAllPlugins()
    const types = new Set<string>()
    
    for (const plugin of plugins) {
      for (const type of plugin.supportedTypes) {
        types.add(type)
      }
    }

    return Array.from(types)
  }

  // Get plugin information
  getPluginInfo(): Array<{
    name: string
    version: string
    description?: string
    supportedTypes: string[]
    enabled: boolean
  }> {
    const plugins = this.pluginManager.getAllPlugins()
    
    return plugins.map(plugin => {
      const config = this.pluginManager.getPluginConfig(plugin.name)
      return {
        name: plugin.name,
        version: plugin.version,
        description: plugin.description,
        supportedTypes: plugin.supportedTypes,
        enabled: !config || config.enabled !== false
      }
    })
  }

  // Enable/disable plugin
  setPluginEnabled(pluginName: string, enabled: boolean): void {
    this.pluginManager.updatePluginConfig(pluginName, { enabled })
  }

  // Set plugin priority
  setPluginPriority(pluginName: string, priority: number): void {
    this.pluginManager.updatePluginConfig(pluginName, { priority })
  }

  // Cleanup
  async cleanup(): Promise<void> {
    await this.pluginManager.cleanup()
    this.initialized = false
  }
}

// Export singleton instance
export const pluginFileProcessor = new PluginFileProcessorService()
