/**
 * Standardized error types for services
 * Provides consistent error handling across all ChatLo services
 */

export enum ServiceErrorCode {
  // General errors
  INITIALIZATION_FAILED = 'INITIALIZATION_FAILED',
  CONFIGURATION_ERROR = 'CONFIGURATION_ERROR',
  VALIDATION_ERROR = 'VAL<PERSON>ATION_ERROR',
  
  // Network/API errors
  NETWORK_ERROR = 'NETWORK_ERROR',
  API_ERROR = 'API_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  RATE_LIMIT_ERROR = 'RATE_LIMIT_ERROR',
  
  // File system errors
  FILE_NOT_FOUND = 'FILE_NOT_FOUND',
  FILE_READ_ERROR = 'FILE_READ_ERROR',
  FILE_WRITE_ERROR = 'FILE_WRITE_ERROR',
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  
  // Service-specific errors
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',
  INVALID_STATE = 'INVALID_STATE',
  OPERATION_FAILED = 'OPERATION_FAILED',
  
  // External service errors
  EXTERNAL_SERVICE_ERROR = 'EXTERNAL_SERVICE_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  
  // Unknown error
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

export interface ServiceErrorContext {
  serviceName: string
  operation?: string
  details?: Record<string, any>
  originalError?: Error
  timestamp?: string
}

export class ServiceError extends Error {
  public readonly code: ServiceErrorCode
  public readonly context: ServiceErrorContext
  public readonly isRetryable: boolean

  constructor(
    code: ServiceErrorCode,
    message: string,
    context: ServiceErrorContext,
    isRetryable: boolean = false
  ) {
    super(message)
    this.name = 'ServiceError'
    this.code = code
    this.context = {
      ...context,
      timestamp: context.timestamp || new Date().toISOString()
    }
    this.isRetryable = isRetryable

    // Maintain proper stack trace
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, ServiceError)
    }
  }

  /**
   * Create a ServiceError from an unknown error
   */
  static fromError(
    error: unknown,
    serviceName: string,
    operation?: string,
    details?: Record<string, any>
  ): ServiceError {
    if (error instanceof ServiceError) {
      return error
    }

    let code = ServiceErrorCode.UNKNOWN_ERROR
    let message = 'An unknown error occurred'
    let isRetryable = false

    if (error instanceof Error) {
      message = error.message
      
      // Map common error patterns to specific codes
      if (error.message.includes('ENOENT') || error.message.includes('not found')) {
        code = ServiceErrorCode.FILE_NOT_FOUND
      } else if (error.message.includes('EACCES') || error.message.includes('permission denied')) {
        code = ServiceErrorCode.PERMISSION_DENIED
      } else if (error.message.includes('timeout')) {
        code = ServiceErrorCode.TIMEOUT_ERROR
        isRetryable = true
      } else if (error.message.includes('network') || error.message.includes('fetch')) {
        code = ServiceErrorCode.NETWORK_ERROR
        isRetryable = true
      } else if (error.message.includes('rate limit')) {
        code = ServiceErrorCode.RATE_LIMIT_ERROR
        isRetryable = true
      }
    } else if (typeof error === 'string') {
      message = error
    } else if (error && typeof error === 'object') {
      message = (error as any).message || JSON.stringify(error)
    }

    return new ServiceError(code, message, {
      serviceName,
      operation,
      details,
      originalError: error instanceof Error ? error : undefined
    }, isRetryable)
  }

  /**
   * Convert to a plain object for logging/serialization
   */
  toJSON(): Record<string, any> {
    return {
      name: this.name,
      code: this.code,
      message: this.message,
      context: this.context,
      isRetryable: this.isRetryable,
      stack: this.stack
    }
  }

  /**
   * Get a user-friendly error message
   */
  getUserMessage(): string {
    switch (this.code) {
      case ServiceErrorCode.NETWORK_ERROR:
        return 'Network connection failed. Please check your internet connection and try again.'
      case ServiceErrorCode.FILE_NOT_FOUND:
        return 'The requested file could not be found.'
      case ServiceErrorCode.PERMISSION_DENIED:
        return 'Permission denied. Please check file permissions.'
      case ServiceErrorCode.RATE_LIMIT_ERROR:
        return 'Too many requests. Please wait a moment and try again.'
      case ServiceErrorCode.AUTHENTICATION_ERROR:
        return 'Authentication failed. Please check your credentials.'
      case ServiceErrorCode.SERVICE_UNAVAILABLE:
        return 'Service is temporarily unavailable. Please try again later.'
      default:
        return this.message
    }
  }
}

/**
 * Result type for service operations
 */
export interface ServiceResult<T> {
  success: boolean
  data?: T
  error?: ServiceError
}

/**
 * Helper function to create success result
 */
export function createSuccessResult<T>(data: T): ServiceResult<T> {
  return { success: true, data }
}

/**
 * Helper function to create error result
 */
export function createErrorResult<T>(error: ServiceError): ServiceResult<T> {
  return { success: false, error }
}

/**
 * Helper function to wrap async operations with error handling
 */
export async function wrapServiceOperation<T>(
  operation: () => Promise<T>,
  serviceName: string,
  operationName?: string
): Promise<ServiceResult<T>> {
  try {
    const result = await operation()
    return createSuccessResult(result)
  } catch (error) {
    const serviceError = ServiceError.fromError(error, serviceName, operationName)
    return createErrorResult(serviceError)
  }
}
